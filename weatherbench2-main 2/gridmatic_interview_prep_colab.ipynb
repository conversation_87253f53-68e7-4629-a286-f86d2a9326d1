{"cells": [{"cell_type": "markdown", "metadata": {"id": "header"}, "source": ["# Gridmatic Interview Prep: WeatherBench2 Concepts for Energy Trading\n", "\n", "## 🎯 **30-Minute Coding Round Preparation**\n", "\n", "Based on your interview, Gridmatic focuses on:\n", "- **Energy Trading**: Predicting energy demand (temperature-dependent) and supply (solar/wind)\n", "- **Physical Assets**: Solar, wind, and battery storage optimization\n", "- **Retail Energy**: Renewable energy contracts\n", "\n", "This tutorial covers WeatherBench2 concepts that directly apply to these use cases.\n", "\n", "---\n", "\n", "## 📚 **Core Concepts You'll Practice**\n", "\n", "1. **Xarray for Weather Data Processing** (Most Important)\n", "2. **Time Series Analysis for Energy Forecasting**\n", "3. **Spatial Analysis for Solar/Wind Resource Assessment**\n", "4. **Data Pi<PERSON>ine <PERSON>** (Apache Beam concepts)\n", "5. **Performance Optimization** (<PERSON>, <PERSON>)\n", "6. **Error Handling & Validation**\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "setup"}, "outputs": [], "source": ["# Essential imports - memorize these patterns from WeatherBench2\n", "import numpy as np\n", "import pandas as pd\n", "import xarray as xr\n", "import matplotlib.pyplot as plt\n", "from typing import Optional, Dict, List, Tuple\n", "from datetime import datetime, timedelta\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Install required packages\n", "!pip install xarray netcdf4 dask matplotlib seaborn -q\n", "\n", "print(\"✅ Setup complete! Ready for Gridmatic-style weather data processing.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---\n", "\n", "# 🌡️ **Section 1: Xarray Fundamentals for Energy Applications**\n", "\n", "**Why This Matters for Gridmatic:**\n", "- Temperature data drives energy demand forecasting\n", "- Wind/solar data drives renewable energy supply prediction\n", "- Multi-dimensional data handling is core to their business\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 🎯 PRACTICE PROBLEM 1: Create Mock Weather Data (WeatherBench2 Style)\n", "# This mimics the schema.py patterns from WeatherBench2\n", "\n", "def create_mock_weather_data(\n", "    start_date: str = '2023-01-01',\n", "    end_date: str = '2023-12-31',\n", "    lat_range: Tuple[float, float] = (25.0, 50.0),\n", "    lon_range: Tuple[float, float] = (-125.0, -65.0)\n", ") -> xr.Dataset:\n", "    \"\"\"\n", "    Create mock weather dataset following WeatherBench2 conventions.\n", "    \n", "    Key WeatherBench2 patterns:\n", "    - Standard dimension names: time, latitude, longitude, level\n", "    - Realistic coordinate ranges\n", "    - Multiple weather variables\n", "    \"\"\"\n", "    # Time coordinate - daily data\n", "    time = pd.date_range(start_date, end_date, freq='D')\n", "    \n", "    # Spatial coordinates (US focus for energy trading)\n", "    latitude = np.linspace(lat_range[0], lat_range[1], 20)\n", "    longitude = np.linspace(lon_range[0], lon_range[1], 30)\n", "    \n", "    # Create realistic weather data\n", "    np.random.seed(42)  # Reproducible for interviews\n", "    \n", "    # Temperature: seasonal variation + daily noise\n", "    temp_base = 15 + 10 * np.sin(2 * np.pi * np.arange(len(time)) / 365.25)\n", "    temperature = np.random.normal(\n", "        temp_base[:, None, None], \n", "        3, \n", "        (len(time), len(latitude), len(longitude))\n", "    )\n", "    \n", "    # Wind speed: realistic distribution\n", "    wind_speed = np.random.weibull(2, (len(time), len(latitude), len(longitude))) * 8\n", "    \n", "    # Solar irradiance: latitude and seasonal dependent\n", "    lat_factor = np.cos(np.radians(latitude))[None, :, None]\n", "    seasonal_factor = 0.5 + 0.5 * np.sin(2 * np.pi * np.arange(len(time)) / 365.25)[:, None, None]\n", "    solar_irradiance = np.random.gamma(2, 200) * lat_factor * seasonal_factor\n", "    \n", "    # Create xarray Dataset (WeatherBench2 style)\n", "    ds = xr.Dataset(\n", "        {\n", "            '2m_temperature': (['time', 'latitude', 'longitude'], temperature),\n", "            '10m_wind_speed': (['time', 'latitude', 'longitude'], wind_speed),\n", "            'solar_irradiance': (['time', 'latitude', 'longitude'], solar_irradiance),\n", "        },\n", "        coords={\n", "            'time': time,\n", "            'latitude': latitude,\n", "            'longitude': longitude,\n", "        },\n", "        attrs={\n", "            'title': 'Mock Weather Data for Energy Applications',\n", "            'source': 'WeatherBench2-style synthetic data'\n", "        }\n", "    )\n", "    \n", "    return ds\n", "\n", "# Create the dataset\n", "weather_data = create_mock_weather_data()\n", "print(\"📊 Mock Weather Dataset Created:\")\n", "print(weather_data)\n", "print(f\"\\n🔍 Data shape: {weather_data.dims}\")\n", "print(f\"📅 Time range: {weather_data.time.min().values} to {weather_data.time.max().values}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 🎯 PRACTICE PROBLEM 2: Data Selection and Filtering (Core WeatherBench2 Pattern)\n", "# This follows the Selection class pattern from weatherbench2/config.py\n", "\n", "def select_weather_data(\n", "    dataset: xr.Dataset,\n", "    variables: List[str],\n", "    time_slice: slice,\n", "    lat_slice: Optional[slice] = None,\n", "    lon_slice: Optional[slice] = None\n", ") -> xr.Dataset:\n", "    \"\"\"\n", "    Select subset of weather data - WeatherBench2 Selection pattern.\n", "    \n", "    This mimics weatherbench2.config.Selection functionality.\n", "    \"\"\"\n", "    # Variable selection\n", "    if variables:\n", "        dataset = dataset[variables]\n", "    \n", "    # Time selection\n", "    dataset = dataset.sel(time=time_slice)\n", "    \n", "    # Spatial selection\n", "    if lat_slice:\n", "        dataset = dataset.sel(latitude=lat_slice)\n", "    if lon_slice:\n", "        dataset = dataset.sel(longitude=lon_slice)\n", "    \n", "    return dataset\n", "\n", "# 🎯 INTERVIEW SCENARIO: Select summer temperature data for Texas region\n", "texas_summer_temp = select_weather_data(\n", "    weather_data,\n", "    variables=['2m_temperature'],\n", "    time_slice=slice('2023-06-01', '2023-08-31'),\n", "    lat_slice=slice(25.8, 36.5),  # Texas latitude range\n", "    lon_slice=slice(-106.6, -93.5)  # Texas longitude range\n", ")\n", "\n", "print(\"🌡️ Texas Summer Temperature Data:\")\n", "print(texas_summer_temp)\n", "print(f\"\\n📈 Average summer temperature: {texas_summer_temp['2m_temperature'].mean().values:.1f}°C\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---\n", "\n", "# ⚡ **Section 2: Energy Demand Forecasting (Temperature-Based)**\n", "\n", "**Gridmatic Use Case:** Predicting energy consumption based on temperature for trading decisions.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 🎯 PRACTICE PROBLEM 3: Energy Demand Modeling\n", "# This applies WeatherBench2 evaluation patterns to energy forecasting\n", "\n", "def calculate_cooling_degree_days(\n", "    temperature: xr.<PERSON>, \n", "    base_temp: float = 18.3  # 65°F in Celsius\n", ") -> xr.<PERSON>:\n", "    \"\"\"\n", "    Calculate Cooling Degree Days (CDD) - key metric for energy demand.\n", "    \n", "    WeatherBench2 pattern: Use xarray operations for efficient computation.\n", "    \"\"\"\n", "    cdd = (temperature - base_temp).where(temperature > base_temp, 0)\n", "    return cdd\n", "\n", "def calculate_heating_degree_days(\n", "    temperature: xr.<PERSON>, \n", "    base_temp: float = 18.3\n", ") -> xr.<PERSON>:\n", "    \"\"\"\n", "    Calculate Heating Degree Days (HDD) - key metric for energy demand.\n", "    \"\"\"\n", "    hdd = (base_temp - temperature).where(temperature < base_temp, 0)\n", "    return hdd\n", "\n", "def estimate_energy_demand(\n", "    temperature: xr.<PERSON>,\n", "    population_density: Optional[xr.<PERSON>] = None\n", ") -> xr.<PERSON>:\n", "    \"\"\"\n", "    Estimate energy demand based on temperature.\n", "    \n", "    Simple model: Base load + Cooling load + Heating load\n", "    \"\"\"\n", "    cdd = calculate_cooling_degree_days(temperature)\n", "    hdd = calculate_heating_degree_days(temperature)\n", "    \n", "    # Simple energy demand model (MWh)\n", "    base_load = 100  # Base consumption\n", "    cooling_factor = 5  # MWh per degree day\n", "    heating_factor = 3  # MWh per degree day\n", "    \n", "    energy_demand = base_load + (cdd * cooling_factor) + (hdd * heating_factor)\n", "    \n", "    # Apply population weighting if available\n", "    if population_density is not None:\n", "        energy_demand = energy_demand * population_density\n", "    \n", "    return energy_demand\n", "\n", "# Calculate energy demand for Texas\n", "texas_energy_demand = estimate_energy_demand(texas_summer_temp['2m_temperature'])\n", "\n", "print(\"⚡ Texas Summer Energy Demand Analysis:\")\n", "print(f\"📊 Average daily demand: {texas_energy_demand.mean().values:.0f} MWh\")\n", "print(f\"📈 Peak demand: {texas_energy_demand.max().values:.0f} MWh\")\n", "print(f\"📉 Minimum demand: {texas_energy_demand.min().values:.0f} MWh\")\n", "\n", "# Time series analysis\n", "daily_avg_demand = texas_energy_demand.mean(dim=['latitude', 'longitude'])\n", "print(f\"\\n📅 Demand trend over summer:\")\n", "print(f\"   June avg: {daily_avg_demand.sel(time=slice('2023-06-01', '2023-06-30')).mean().values:.0f} MWh\")\n", "print(f\"   July avg: {daily_avg_demand.sel(time=slice('2023-07-01', '2023-07-31')).mean().values:.0f} MWh\")\n", "print(f\"   August avg: {daily_avg_demand.sel(time=slice('2023-08-01', '2023-08-31')).mean().values:.0f} MWh\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---\n", "\n", "# 🌞 **Section 3: Renewable Energy Resource Assessment**\n", "\n", "**Gridmatic Use Case:** Optimizing solar and wind asset performance for trading.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 🎯 PRACTICE PROBLEM 4: Solar and Wind Resource Analysis\n", "# This applies WeatherBench2 spatial analysis patterns\n", "\n", "def calculate_solar_power_potential(\n", "    irradiance: xr.<PERSON>,\n", "    panel_efficiency: float = 0.20,\n", "    panel_area: float = 1000  # m²\n", ") -> xr.<PERSON>:\n", "    \"\"\"\n", "    Calculate solar power generation potential.\n", "    \n", "    WeatherBench2 pattern: Vectorized operations on xarray.\n", "    \"\"\"\n", "    # Convert W/m² to MW (assuming panel_area in m²)\n", "    power_mw = (irradiance * panel_efficiency * panel_area) / 1e6\n", "    return power_mw\n", "\n", "def calculate_wind_power_potential(\n", "    wind_speed: xr.<PERSON>,\n", "    turbine_capacity: float = 2.5,  # MW\n", "    cut_in_speed: float = 3.0,      # m/s\n", "    rated_speed: float = 12.0,      # m/s\n", "    cut_out_speed: float = 25.0     # m/s\n", ") -> xr.<PERSON>:\n", "    \"\"\"\n", "    Calculate wind power generation using simplified power curve.\n", "    \n", "    WeatherBench2 pattern: Complex conditional operations with xarray.\n", "    \"\"\"\n", "    # Initialize power output\n", "    power = xr.zeros_like(wind_speed)\n", "    \n", "    # Power curve logic\n", "    # Below cut-in: 0 power\n", "    # Cut-in to rated: cubic relationship\n", "    # Rated to cut-out: full capacity\n", "    # Above cut-out: 0 power (safety shutdown)\n", "    \n", "    # Cubic region (cut-in to rated speed)\n", "    cubic_mask = (wind_speed >= cut_in_speed) & (wind_speed < rated_speed)\n", "    power = power.where(\n", "        ~cubic_mask,\n", "        turbine_capacity * ((wind_speed - cut_in_speed) / (rated_speed - cut_in_speed)) ** 3\n", "    )\n", "    \n", "    # Rated region (rated to cut-out speed)\n", "    rated_mask = (wind_speed >= rated_speed) & (wind_speed < cut_out_speed)\n", "    power = power.where(~rated_mask, turbine_capacity)\n", "    \n", "    return power\n", "\n", "# Calculate renewable potential for full dataset\n", "solar_potential = calculate_solar_power_potential(weather_data['solar_irradiance'])\n", "wind_potential = calculate_wind_power_potential(weather_data['10m_wind_speed'])\n", "\n", "# Add to dataset\n", "weather_data['solar_power_mw'] = solar_potential\n", "weather_data['wind_power_mw'] = wind_potential\n", "\n", "print(\"🌞 Renewable Energy Resource Analysis:\")\n", "print(f\"☀️ Average solar potential: {solar_potential.mean().values:.2f} MW\")\n", "print(f\"💨 Average wind potential: {wind_potential.mean().values:.2f} MW\")\n", "print(f\"⚡ Total renewable potential: {(solar_potential + wind_potential).mean().values:.2f} MW\")\n", "\n", "# Seasonal analysis (key for Gridmatic's trading strategies)\n", "seasonal_analysis = weather_data.groupby('time.season').mean()\n", "print(\"\\n📊 Seasonal Renewable Potential:\")\n", "for season in ['DJ<PERSON>', 'MA<PERSON>', 'JJA', 'SON']:\n", "    solar_avg = seasonal_analysis['solar_power_mw'].sel(season=season).mean().values\n", "    wind_avg = seasonal_analysis['wind_power_mw'].sel(season=season).mean().values\n", "    print(f\"   {season}: Solar {solar_avg:.1f} MW, Wind {wind_avg:.1f} MW\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---\n", "\n", "# 🔄 **Section 4: <PERSON> Pi<PERSON>ine <PERSON> (Apache Beam Concepts)**\n", "\n", "**Gridmatic Use Case:** Processing large weather datasets efficiently for real-time trading decisions.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 🎯 PRACTICE PROBLEM 5: Data Processing Pipeline (WeatherBench2 Style)\n", "# This mimics the pipeline patterns from WeatherBench2 scripts\n", "\n", "from typing import Callable, Any\n", "from functools import partial\n", "\n", "def process_weather_chunk(\n", "    chunk: xr.Dataset,\n", "    operations: List[Callable[[xr.Dataset], xr.Dataset]]\n", ") -> xr.Dataset:\n", "    \"\"\"\n", "    Process a chunk of weather data through a pipeline of operations.\n", "    \n", "    WeatherBench2 pattern: Functional pipeline processing.\n", "    \"\"\"\n", "    result = chunk\n", "    for operation in operations:\n", "        result = operation(result)\n", "    return result\n", "\n", "def validate_weather_data(dataset: xr.Dataset) -> xr.Dataset:\n", "    \"\"\"\n", "    Validate weather data - WeatherBench2 error handling pattern.\n", "    \"\"\"\n", "    # Check for required variables\n", "    required_vars = ['2m_temperature', '10m_wind_speed', 'solar_irradiance']\n", "    missing_vars = [var for var in required_vars if var not in dataset.data_vars]\n", "    if missing_vars:\n", "        raise ValueError(f\"Missing required variables: {missing_vars}\")\n", "    \n", "    # Check for NaN values\n", "    for var in required_vars:\n", "        if dataset[var].isnull().any():\n", "            print(f\"⚠️  Warning: {var} contains NaN values\")\n", "    \n", "    # Check coordinate ranges\n", "    if dataset.latitude.min() < -90 or dataset.latitude.max() > 90:\n", "        raise ValueError(\"Invalid latitude range\")\n", "    if dataset.longitude.min() < -180 or dataset.longitude.max() > 180:\n", "        raise ValueError(\"Invalid longitude range\")\n", "    \n", "    return dataset\n", "\n", "def compute_energy_metrics(dataset: xr.Dataset) -> xr.Dataset:\n", "    \"\"\"\n", "    Compute energy-related metrics - core business logic.\n", "    \"\"\"\n", "    # Add energy demand\n", "    dataset['energy_demand_mwh'] = estimate_energy_demand(dataset['2m_temperature'])\n", "    \n", "    # Add renewable potential\n", "    dataset['solar_power_mw'] = calculate_solar_power_potential(dataset['solar_irradiance'])\n", "    dataset['wind_power_mw'] = calculate_wind_power_potential(dataset['10m_wind_speed'])\n", "    \n", "    # Compute net energy balance\n", "    dataset['net_energy_balance'] = (\n", "        dataset['solar_power_mw'] + dataset['wind_power_mw'] - dataset['energy_demand_mwh']\n", "    )\n", "    \n", "    return dataset\n", "\n", "def aggregate_regional_data(dataset: xr.Dataset) -> xr.Dataset:\n", "    \"\"\"\n", "    Aggregate data by regions - WeatherBench2 spatial averaging pattern.\n", "    \"\"\"\n", "    # Simple regional aggregation (could be more sophisticated)\n", "    regional_data = dataset.mean(dim=['latitude', 'longitude'])\n", "    regional_data.attrs['aggregation'] = 'spatial_mean'\n", "    return regional_data\n", "\n", "# 🎯 INTERVIEW SCENARIO: Build and execute processing pipeline\n", "pipeline_operations = [\n", "    validate_weather_data,\n", "    compute_energy_metrics,\n", "    aggregate_regional_data\n", "]\n", "\n", "# Process a subset of data\n", "sample_data = weather_data.isel(time=slice(0, 30))  # First 30 days\n", "processed_data = process_weather_chunk(sample_data, pipeline_operations)\n", "\n", "print(\"🔄 Data Pipeline Results:\")\n", "print(f\"📊 Processed variables: {list(processed_data.data_vars)}\")\n", "print(f\"⚡ Average energy demand: {processed_data['energy_demand_mwh'].mean().values:.0f} MWh\")\n", "print(f\"🌞 Average solar generation: {processed_data['solar_power_mw'].mean().values:.1f} MW\")\n", "print(f\"💨 Average wind generation: {processed_data['wind_power_mw'].mean().values:.1f} MW\")\n", "print(f\"⚖️  Average net balance: {processed_data['net_energy_balance'].mean().values:.1f} MW\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---\n", "\n", "# 🚀 **Section 5: Performance Optimization (WeatherBench2 Patterns)**\n", "\n", "**Gridmatic Use Case:** Handling large-scale weather data efficiently for real-time trading.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 🎯 PRACTICE PROBLEM 6: Chunking and Dask Integration\n", "# This follows WeatherBench2 performance optimization patterns\n", "\n", "import dask\n", "import dask.array as da\n", "\n", "def optimize_dataset_chunks(\n", "    dataset: xr.Dataset,\n", "    chunk_sizes: Dict[str, int] = None\n", ") -> xr.Dataset:\n", "    \"\"\"\n", "    Optimize dataset chunking for performance.\n", "    \n", "    WeatherBench2 pattern: Explicit chunk management for distributed processing.\n", "    \"\"\"\n", "    if chunk_sizes is None:\n", "        # Default chunking strategy\n", "        chunk_sizes = {\n", "            'time': 30,      # Monthly chunks\n", "            'latitude': 10,  # Spatial chunks\n", "            'longitude': 15  # Spatial chunks\n", "        }\n", "    \n", "    # Apply chunking\n", "    chunked_dataset = dataset.chunk(chunk_sizes)\n", "    \n", "    print(f\"📦 Chunking applied: {chunk_sizes}\")\n", "    print(f\"💾 Memory usage per chunk: ~{estimate_chunk_memory(chunked_dataset):.1f} MB\")\n", "    \n", "    return chunked_dataset\n", "\n", "def estimate_chunk_memory(dataset: xr.Dataset) -> float:\n", "    \"\"\"\n", "    Estimate memory usage per chunk in MB.\n", "    \"\"\"\n", "    total_elements = 1\n", "    for dim, size in dataset.chunks.items():\n", "        if isinstance(size, tuple):\n", "            total_elements *= size[0]  # First chunk size\n", "        else:\n", "            total_elements *= size\n", "    \n", "    # Assume float64 (8 bytes) for each variable\n", "    bytes_per_chunk = total_elements * len(dataset.data_vars) * 8\n", "    return bytes_per_chunk / (1024 * 1024)  # Convert to MB\n", "\n", "def parallel_computation_example(dataset: xr.Dataset) -> Dict[str, float]:\n", "    \"\"\"\n", "    Example of parallel computation using Dask.\n", "    \n", "    WeatherBench2 pattern: Lazy evaluation with Das<PERSON>.\n", "    \"\"\"\n", "    # Chunk the dataset\n", "    chunked_ds = optimize_dataset_chunks(dataset)\n", "    \n", "    # Define computations (lazy evaluation)\n", "    computations = {\n", "        'mean_temp': chunked_ds['2m_temperature'].mean(),\n", "        'max_wind': chunked_ds['10m_wind_speed'].max(),\n", "        'total_solar': chunked_ds['solar_irradiance'].sum(),\n", "        'std_temp': chunked_ds['2m_temperature'].std()\n", "    }\n", "    \n", "    # Execute computations in parallel\n", "    with dask.config.set(scheduler='threads', num_workers=2):\n", "        results = dask.compute(computations)[0]\n", "    \n", "    # Convert to regular values\n", "    return {k: float(v.values) for k, v in results.items()}\n", "\n", "# 🎯 INTERVIEW SCENARIO: Performance optimization\n", "print(\"🚀 Performance Optimization Demo:\")\n", "\n", "# Original dataset info\n", "print(f\"📊 Original dataset size: {weather_data.nbytes / 1e6:.1f} MB\")\n", "\n", "# Optimize and compute\n", "results = parallel_computation_example(weather_data)\n", "\n", "print(\"\\n📈 Parallel Computation Results:\")\n", "for metric, value in results.items():\n", "    print(f\"   {metric}: {value:.2f}\")\n", "\n", "# Memory-efficient processing tip\n", "print(\"\\n💡 Performance Tips for Gridmatic Interview:\")\n", "print(\"   1. Use chunking for large datasets\")\n", "print(\"   2. Leverage lazy evaluation with <PERSON><PERSON>\")\n", "print(\"   3. Optimize chunk sizes for your computation pattern\")\n", "print(\"   4. Use vectorized operations (avoid loops)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---\n", "\n", "# 🎯 **Section 6: Interview Practice Problems**\n", "\n", "**30-Minute Coding Round Scenarios Based on WeatherBench2 Patterns**\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 🎯 PRACTICE PROBLEM 7: Real-time Energy Trading Decision\n", "# Scenario: Given current weather conditions, should Gridmatic buy or sell energy?\n", "\n", "def make_trading_decision(\n", "    current_weather: xr.Data<PERSON>,\n", "    price_per_mwh: float = 50.0,\n", "    profit_threshold: float = 0.1\n", ") -> Dict[str, Any]:\n", "    \"\"\"\n", "    Make energy trading decision based on weather forecast.\n", "    \n", "    WeatherBench2 pattern: Complex decision logic with data validation.\n", "    \"\"\"\n", "    # Validate input data\n", "    required_vars = ['2m_temperature', '10m_wind_speed', 'solar_irradiance']\n", "    for var in required_vars:\n", "        if var not in current_weather.data_vars:\n", "            raise ValueError(f\"Missing required variable: {var}\")\n", "    \n", "    # Calculate energy metrics\n", "    demand = estimate_energy_demand(current_weather['2m_temperature'])\n", "    solar_gen = calculate_solar_power_potential(current_weather['solar_irradiance'])\n", "    wind_gen = calculate_wind_power_potential(current_weather['10m_wind_speed'])\n", "    \n", "    # Calculate net position\n", "    total_generation = solar_gen + wind_gen\n", "    net_position = total_generation - demand  # Positive = surplus, Negative = deficit\n", "    \n", "    # Calculate potential profit/loss\n", "    potential_revenue = net_position * price_per_mwh\n", "    \n", "    # Make decision\n", "    avg_net_position = float(net_position.mean().values)\n", "    avg_revenue = float(potential_revenue.mean().values)\n", "    \n", "    if avg_net_position > 0 and avg_revenue > profit_threshold * price_per_mwh:\n", "        decision = \"SELL\"\n", "        confidence = min(avg_net_position / 100, 1.0)  # Normalize confidence\n", "    elif avg_net_position < 0 and abs(avg_revenue) > profit_threshold * price_per_mwh:\n", "        decision = \"BUY\"\n", "        confidence = min(abs(avg_net_position) / 100, 1.0)\n", "    else:\n", "        decision = \"HOLD\"\n", "        confidence = 0.5\n", "    \n", "    return {\n", "        'decision': decision,\n", "        'confidence': confidence,\n", "        'net_position_mw': avg_net_position,\n", "        'expected_revenue': avg_revenue,\n", "        'demand_forecast': float(demand.mean().values),\n", "        'generation_forecast': float(total_generation.mean().values)\n", "    }\n", "\n", "# Test the trading algorithm\n", "sample_weather = weather_data.isel(time=slice(0, 7))  # One week\n", "trading_result = make_trading_decision(sample_weather)\n", "\n", "print(\"📈 Energy Trading Decision:\")\n", "print(f\"🎯 Decision: {trading_result['decision']}\")\n", "print(f\"🎲 Confidence: {trading_result['confidence']:.2f}\")\n", "print(f\"⚡ Net Position: {trading_result['net_position_mw']:.1f} MW\")\n", "print(f\"💰 Expected Revenue: ${trading_result['expected_revenue']:.0f}\")\n", "print(f\"📊 Demand Forecast: {trading_result['demand_forecast']:.0f} MWh\")\n", "print(f\"🔋 Generation Forecast: {trading_result['generation_forecast']:.1f} MW\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---\n", "\n", "# 📝 **Interview Success Checklist**\n", "\n", "## 🔥 **Key WeatherBench2 Patterns to Remember:**\n", "\n", "### 1. **Data Handling (Most Important)**\n", "```python\n", "# Always use these imports\n", "import xarray as xr\n", "import numpy as np\n", "import pandas as pd\n", "from typing import Optional, Dict, List\n", "\n", "# Standard dimension names\n", "dims = ['time', 'latitude', 'longitude', 'level']\n", "\n", "# Data selection pattern\n", "subset = dataset.sel(time=slice('2023-01-01', '2023-12-31'))\n", "subset = subset.sel(latitude=slice(25, 50), longitude=slice(-125, -65))\n", "```\n", "\n", "### 2. **Error Handling**\n", "```python\n", "# Always validate inputs\n", "if 'temperature' not in dataset.data_vars:\n", "    raise ValueError(\"Missing required variable: temperature\")\n", "\n", "# Check for NaN values\n", "if dataset['temperature'].isnull().any():\n", "    print(\"Warning: Data contains NaN values\")\n", "```\n", "\n", "### 3. **Performance Optimization**\n", "```python\n", "# Use chunking for large datasets\n", "chunked_data = dataset.chunk({'time': 30, 'latitude': 10})\n", "\n", "# Vectorized operations (avoid loops)\n", "result = dataset.where(dataset > threshold, 0)\n", "```\n", "\n", "### 4. **Function Structure**\n", "```python\n", "def process_weather_data(\n", "    dataset: xr.Dataset,\n", "    variable: str,\n", "    threshold: Optional[float] = None\n", ") -> xr.Dataset:\n", "    \\\"\\\"\\\"Process weather data following WeatherBench2 patterns.\\\"\\\"\\\"\\n\",\n", "    # Validation\n", "    if variable not in dataset.data_vars:\n", "        raise ValueError(f\"Variable {variable} not found\")\n", "    \n", "    # Processing\n", "    result = dataset.copy()  # Don't modify original\n", "    # ... processing logic ...\n", "    \n", "    return result\n", "```\n", "\n", "## ⚡ **30-Minute Interview Strategy:**\n", "\n", "1. **First 5 minutes:** Understand the problem, ask clarifying questions\n", "2. **Next 15 minutes:** Write core logic with proper error handling\n", "3. **Next 5 minutes:** Add performance optimizations if needed\n", "4. **Last 5 minutes:** Test with sample data, explain your approach\n", "\n", "## 🎯 **Common Interview Topics for Gridmatic:**\n", "\n", "- **Temperature-based energy demand forecasting**\n", "- **Solar/wind resource assessment**\n", "- **Time series analysis and seasonal patterns**\n", "- **Spatial data aggregation and analysis**\n", "- **Data validation and error handling**\n", "- **Performance optimization for large datasets**\n", "\n", "## 💡 **Pro Tips:**\n", "\n", "- **Show your xarray expertise** - it's core to their weather data processing\n", "- **Mention <PERSON> Beam** - they specifically asked about it\n", "- **Think about energy trading applications** - connect weather to business value\n", "- **Use WeatherBench2 naming conventions** - shows you understand the domain\n", "- **Handle edge cases** - NaN values, missing data, invalid coordinates\n", "\n", "---\n", "\n", "# 🚀 **You're Ready!**\n", "\n", "This tutorial covers all the key WeatherBench2 concepts that apply to Gridmatic's energy trading business. Practice these patterns, and you'll demonstrate exactly the kind of weather data processing expertise they're looking for.\n", "\n", "**Good luck with your interview! 🍀**\n"]}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}