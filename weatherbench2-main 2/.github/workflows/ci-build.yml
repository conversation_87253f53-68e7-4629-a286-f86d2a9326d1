name: CI

on:
  # Triggers the workflow on push or pull request events but only for the main branch
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]
  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:

jobs:
  build:
    name: "python ${{ matrix.python-version }} tests"
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        python-version: ["3.11"]
    steps:
    - name: Cancel previous
      uses: styfle/cancel-workflow-action@0.7.0
      with:
        access_token: ${{ github.token }}
      if: ${{github.ref != 'refs/head/main'}}
    - uses: actions/checkout@v2
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v2
      with:
        python-version: ${{ matrix.python-version }}
    - name: Get pip cache dir
      id: pip-cache
      run: |
        python -m pip install --upgrade pip wheel
        echo "::set-output name=dir::$(pip cache dir)"
    - name: pip cache
      uses: actions/cache@v4
      with:
        path: ${{ steps.pip-cache.outputs.dir }}
        key: ${{ runner.os }}-pip-${{ hashFiles('**/setup.py') }}
    - name: Install Weatherbench 2
      run: |
        pip install -e .[tests]
    - name: Run unit tests
      run: |
        pytest weatherbench2
    - name: Run scripts tests
      # The scripts define some of the same flags, so we run pytest in separate processes.
      run: |
        for test in scripts/*_test.py; do pytest $test; done
