name: Lin<PERSON>

on:
  # Triggers the workflow on push or pull request events but only for the main branch
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]
  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:

jobs:
  build:
    name: "python ${{ matrix.python-version }} lint"
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        python-version: ["3.11"]
    steps:
    - name: Cancel previous
      uses: styfle/cancel-workflow-action@0.7.0
      with:
        access_token: ${{ github.token }}
      if: ${{github.ref != 'refs/head/main'}}
    - uses: actions/checkout@v4
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
    - name: Get pip cache dir
      id: pip-cache
      run: |
        python -m pip install --upgrade pip wheel
        echo "::set-output name=dir::$(pip cache dir)"
    - name: pip cache
      uses: actions/cache@v4
      with:
        path: ${{ steps.pip-cache.outputs.dir }}
        key: ${{ runner.os }}-pip-${{ hashFiles('**/setup.py') }}
    - name: Install Weatherbench 2
      run: |
        pip install -e .[tests]
    - name: Lint with pyink
      run: |
        pyink --check --diff --color .
