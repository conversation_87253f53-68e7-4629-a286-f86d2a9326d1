{"cells": [{"cell_type": "markdown", "metadata": {"id": "header"}, "source": ["# Python Fundamentals for WeatherBench2\n", "\n", "## 🐍 **Master Python Data Structures & OOP Patterns**\n", "\n", "This comprehensive tutorial covers all Python data structures and object-oriented programming patterns used in WeatherBench2. Perfect for quickly understanding and practicing the core Python concepts you'll need for weather data analysis and coding interviews.\n", "\n", "---\n", "\n", "## 📚 **What You'll Learn**\n", "\n", "1. **Core Data Structures** - Lists, dicts, sets, tuples, and their usage patterns\n", "2. **Advanced Collections** - defaultdict, Counter, deque, and specialized containers\n", "3. **Object-Oriented Design** - Classes, inheritance, dataclasses, and protocols\n", "4. **Functional Programming** - List comprehensions, generators, decorators\n", "5. **Performance Patterns** - Efficient algorithms and memory optimization\n", "6. **Error Handling** - Robust code with proper exception handling\n", "7. **WeatherBench2 Patterns** - Real patterns from the codebase\n", "8. **Interview Preparation** - Common coding patterns and best practices\n", "\n", "Based on actual patterns from the WeatherBench2 codebase.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "setup"}, "outputs": [], "source": ["# Essential imports for Python fundamentals\n", "import sys\n", "import time\n", "import functools\n", "import itertools\n", "import dataclasses\n", "from typing import Dict, List, Set, Tuple, Optional, Union, Any, Protocol\n", "from collections import defaultdict, Counter, deque, namedtuple\n", "from abc import ABC, abstractmethod\n", "from datetime import datetime, timedelta\n", "import numpy as np\n", "import pandas as pd\n", "\n", "# For demonstration purposes\n", "import random\n", "import json\n", "from pathlib import Path\n", "\n", "print(\"✅ Python fundamentals environment ready!\")\n", "print(f\"🐍 Python version: {sys.version.split()[0]}\")\n", "print(f\"📦 NumPy version: {np.__version__}\")\n", "print(f\"📊 Pandas version: {pd.__version__}\")\n", "\n", "# Set random seed for reproducible examples\n", "random.seed(42)\n", "np.random.seed(42)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---\n", "\n", "# 📋 **Section 1: Core Data Structures**\n", "\n", "Master the fundamental Python data structures used extensively in WeatherBench2.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Lists - Dynamic arrays, most common data structure\n", "print(\"📋 Lists - Dynamic Arrays:\")\n", "\n", "# Weather station data example\n", "weather_stations = ['NYC', 'LA', 'Chicago', 'Houston', 'Phoenix']\n", "temperatures = [22.5, 28.1, 15.3, 31.2, 35.8]\n", "readings = []\n", "\n", "print(f\"\\n🌡️ Weather Stations: {weather_stations}\")\n", "print(f\"📊 Temperatures: {temperatures}\")\n", "\n", "# List operations - essential patterns\n", "# Adding data\n", "readings.append({'station': 'NYC', 'temp': 22.5, 'time': '2023-01-01T12:00'})\n", "readings.extend([\n", "    {'station': 'LA', 'temp': 28.1, 'time': '2023-01-01T12:00'},\n", "    {'station': 'Chicago', 'temp': 15.3, 'time': '2023-01-01T12:00'}\n", "])\n", "\n", "print(f\"\\n📝 Readings collected: {len(readings)}\")\n", "\n", "# List comprehensions - THE most important Python pattern\n", "# Filter temperatures above 20°C\n", "warm_temps = [temp for temp in temperatures if temp > 20]\n", "print(f\"🔥 Warm temperatures: {warm_temps}\")\n", "\n", "# Convert <PERSON><PERSON><PERSON> to Fahrenheit\n", "temps_fahrenheit = [temp * 9/5 + 32 for temp in temperatures]\n", "print(f\"🌡️ Fahrenheit: {[f'{t:.1f}°F' for t in temps_fahrenheit]}\")\n", "\n", "# Complex list comprehension with condition\n", "station_temp_pairs = [\n", "    f\"{station}: {temp}°C\" \n", "    for station, temp in zip(weather_stations, temperatures) \n", "    if temp > 20\n", "]\n", "print(f\"🏙️ Warm cities: {station_temp_pairs}\")\n", "\n", "# Nested list comprehensions (2D data)\n", "# Simulate hourly temperatures for 3 days\n", "daily_temps = [\n", "    [20 + 5 * np.sin(hour * np.pi / 12) + np.random.normal(0, 2) for hour in range(24)]\n", "    for day in range(3)\n", "]\n", "print(f\"\\n📅 3-day temperature matrix shape: {len(daily_temps)} days × {len(daily_temps[0])} hours\")\n", "print(f\"📊 Day 1 average: {np.mean(daily_temps[0]):.1f}°C\")\n", "\n", "# List slicing - essential for time series\n", "recent_temps = temperatures[-3:]  # Last 3 readings\n", "first_half = temperatures[:len(temperatures)//2]\n", "every_other = temperatures[::2]\n", "\n", "print(f\"\\n✂️ Recent temps: {recent_temps}\")\n", "print(f\"📈 First half: {first_half}\")\n", "print(f\"🔢 Every other: {every_other}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Dictionaries - Key-value mappings, extremely important\n", "print(\"🗂️ Dictionaries - Key-Value Mappings:\")\n", "\n", "# Weather station metadata\n", "station_info = {\n", "    'NYC': {'lat': 40.7128, 'lon': -74.0060, 'elevation': 10},\n", "    'LA': {'lat': 34.0522, 'lon': -118.2437, 'elevation': 71},\n", "    'Chicago': {'lat': 41.8781, 'lon': -87.6298, 'elevation': 182}\n", "}\n", "\n", "print(f\"\\n🏙️ Station Info:\")\n", "for city, info in station_info.items():\n", "    print(f\"   {city}: {info['lat']:.2f}°N, {info['lon']:.2f}°E, {info['elevation']}m\")\n", "\n", "# Dictionary comprehensions - very powerful\n", "# Create elevation lookup\n", "elevations = {city: info['elevation'] for city, info in station_info.items()}\n", "print(f\"\\n⛰️ Elevations: {elevations}\")\n", "\n", "# Filter high elevation stations\n", "high_elevation = {\n", "    city: info for city, info in station_info.items() \n", "    if info['elevation'] > 100\n", "}\n", "print(f\"🏔️ High elevation stations: {list(high_elevation.keys())}\")\n", "\n", "# Dictionary methods - essential patterns\n", "# Safe access with get()\n", "denver_info = station_info.get('Denver', {'lat': 0, 'lon': 0, 'elevation': 0})\n", "print(f\"\\n🏔️ Denver info (with default): {denver_info}\")\n", "\n", "# Update and merge dictionaries\n", "new_stations = {\n", "    'Denver': {'lat': 39.7392, 'lon': -104.9903, 'elevation': 1609},\n", "    'Miami': {'lat': 25.7617, 'lon': -80.1918, 'elevation': 2}\n", "}\n", "station_info.update(new_stations)\n", "print(f\"📍 Total stations: {len(station_info)}\")\n", "\n", "# Dictionary unpacking (Python 3.5+)\n", "all_stations = {**station_info, 'Seattle': {'lat': 47.6062, 'lon': -122.3321, 'elevation': 56}}\n", "print(f\"🌐 All stations: {len(all_stations)}\")\n", "\n", "# Nested dictionary access\n", "def get_station_coordinate(stations, city, coord):\n", "    \"\"\"Safely get station coordinate with error handling.\"\"\"\n", "    try:\n", "        return stations[city][coord]\n", "    except KeyError as e:\n", "        print(f\"⚠️ Missing data: {e}\")\n", "        return None\n", "\n", "nyc_lat = get_station_coordinate(station_info, 'NYC', 'lat')\n", "missing_coord = get_station_coordinate(station_info, 'Boston', 'lat')\n", "print(f\"\\n📍 NYC latitude: {nyc_lat}\")\n", "print(f\"❌ Boston coordinate: {missing_coord}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Sets - Unique collections, great for filtering\n", "print(\"🔗 Sets - Unique Collections:\")\n", "\n", "# Weather conditions from multiple stations\n", "station_a_conditions = {'sunny', 'cloudy', 'rainy', 'sunny', 'foggy'}\n", "station_b_conditions = {'cloudy', 'rainy', 'snowy', 'windy'}\n", "station_c_conditions = {'sunny', 'windy', 'clear', 'foggy'}\n", "\n", "print(f\"\\n🌤️ Station A conditions: {station_a_conditions}\")\n", "print(f\"⛅ Station B conditions: {station_b_conditions}\")\n", "print(f\"☀️ Station C conditions: {station_c_conditions}\")\n", "\n", "# Set operations - very useful for data analysis\n", "all_conditions = station_a_conditions | station_b_conditions | station_c_conditions\n", "common_conditions = station_a_conditions & station_b_conditions\n", "unique_to_a = station_a_conditions - station_b_conditions\n", "\n", "print(f\"\\n🌍 All unique conditions: {all_conditions}\")\n", "print(f\"🤝 Common A & B: {common_conditions}\")\n", "print(f\"🔸 Unique to A: {unique_to_a}\")\n", "\n", "# Practical example: Find stations with specific conditions\n", "weather_data = [\n", "    {'station': 'NYC', 'condition': 'rainy'},\n", "    {'station': 'LA', 'condition': 'sunny'},\n", "    {'station': 'Chicago', 'condition': 'snowy'},\n", "    {'station': 'Houston', 'condition': 'sunny'},\n", "    {'station': 'Phoenix', 'condition': 'clear'}\n", "]\n", "\n", "# Get unique conditions\n", "unique_conditions = {record['condition'] for record in weather_data}\n", "print(f\"\\n🌈 Unique conditions today: {unique_conditions}\")\n", "\n", "# Find sunny stations\n", "sunny_stations = {record['station'] for record in weather_data if record['condition'] == 'sunny'}\n", "print(f\"☀️ Sunny stations: {sunny_stations}\")\n", "\n", "# Tuples - Immutable sequences, great for coordinates\n", "print(f\"\\n📍 Tuples - Immutable Sequences:\")\n", "\n", "# Coordinate pairs\n", "coordinates = [\n", "    (40.7128, -74.0060),  # NYC\n", "    (34.0522, -118.2437), # LA\n", "    (41.8781, -87.6298)   # Chicago\n", "]\n", "\n", "print(f\"🗺️ Coordinates: {coordinates}\")\n", "\n", "# Tuple unpacking - very elegant\n", "for i, (lat, lon) in enumerate(coordinates):\n", "    city = ['NYC', 'LA', 'Chicago'][i]\n", "    print(f\"   {city}: {lat:.2f}°N, {lon:.2f}°E\")\n", "\n", "# Named tuples - structured data without classes\n", "WeatherReading = namedtuple('WeatherReading', ['station', 'temp', 'humidity', 'pressure'])\n", "\n", "reading1 = WeatherReading('NYC', 22.5, 65, 1013.2)\n", "reading2 = WeatherReading('LA', 28.1, 45, 1015.8)\n", "\n", "print(f\"\\n📊 Named tuple reading: {reading1}\")\n", "print(f\"🌡️ Temperature: {reading1.temp}°C\")\n", "print(f\"💧 Humidity: {reading1.humidity}%\")\n", "\n", "# Convert to dictionary\n", "reading_dict = reading1._asdict()\n", "print(f\"📝 As dictionary: {reading_dict}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---\n", "\n", "# 🧰 **Section 2: Advanced Collections**\n", "\n", "Specialized data structures that make Python code more efficient and elegant.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# defaultdict - Automatic default values, extremely useful\n", "print(\"🔧 defaultdict - Automatic Default Values:\")\n", "\n", "# Group weather readings by station\n", "station_readings = defaultdict(list)\n", "\n", "# Sample weather data\n", "raw_readings = [\n", "    {'station': 'NYC', 'temp': 22.5, 'time': '12:00'},\n", "    {'station': 'LA', 'temp': 28.1, 'time': '12:00'},\n", "    {'station': 'NYC', 'temp': 23.1, 'time': '13:00'},\n", "    {'station': 'Chicago', 'temp': 15.3, 'time': '12:00'},\n", "    {'station': 'LA', 'temp': 29.2, 'time': '13:00'},\n", "    {'station': 'NYC', 'temp': 21.8, 'time': '14:00'}\n", "]\n", "\n", "# Group by station - no need to check if key exists!\n", "for reading in raw_readings:\n", "    station_readings[reading['station']].append(reading)\n", "\n", "print(f\"\\n📊 Readings per station:\")\n", "for station, readings in station_readings.items():\n", "    temps = [r['temp'] for r in readings]\n", "    avg_temp = sum(temps) / len(temps)\n", "    print(f\"   {station}: {len(readings)} readings, avg {avg_temp:.1f}°C\")\n", "\n", "# defaultdict with different types\n", "station_stats = defaultdict(lambda: {'count': 0, 'total_temp': 0, 'conditions': set()})\n", "\n", "weather_observations = [\n", "    {'station': 'NYC', 'temp': 22.5, 'condition': 'cloudy'},\n", "    {'station': 'NYC', 'temp': 23.1, 'condition': 'rainy'},\n", "    {'station': 'LA', 'temp': 28.1, 'condition': 'sunny'},\n", "    {'station': 'LA', 'temp': 29.2, 'condition': 'clear'}\n", "]\n", "\n", "for obs in weather_observations:\n", "    station = obs['station']\n", "    station_stats[station]['count'] += 1\n", "    station_stats[station]['total_temp'] += obs['temp']\n", "    station_stats[station]['conditions'].add(obs['condition'])\n", "\n", "print(f\"\\n📈 Station Statistics:\")\n", "for station, stats in station_stats.items():\n", "    avg_temp = stats['total_temp'] / stats['count']\n", "    conditions = ', '.join(stats['conditions'])\n", "    print(f\"   {station}: {avg_temp:.1f}°C avg, conditions: {conditions}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Counter - Count occurrences, perfect for frequency analysis\n", "print(\"🔢 Counter - Frequency Analysis:\")\n", "\n", "# Weather conditions frequency\n", "daily_conditions = [\n", "    'sunny', 'cloudy', 'rainy', 'sunny', 'clear', 'foggy',\n", "    'sunny', 'cloudy', 'windy', 'sunny', 'rainy', 'clear'\n", "]\n", "\n", "condition_counts = Counter(daily_conditions)\n", "print(f\"\\n🌤️ Weather condition frequencies:\")\n", "for condition, count in condition_counts.most_common():\n", "    percentage = (count / len(daily_conditions)) * 100\n", "    print(f\"   {condition}: {count} times ({percentage:.1f}%)\")\n", "\n", "# Most common conditions\n", "top_3_conditions = condition_counts.most_common(3)\n", "print(f\"\\n🏆 Top 3 conditions: {top_3_conditions}\")\n", "\n", "# Counter arithmetic\n", "morning_conditions = Counter(['sunny', 'cloudy', 'sunny', 'clear'])\n", "afternoon_conditions = Counter(['cloudy', 'rainy', 'sunny', 'windy'])\n", "\n", "# Combine counters\n", "daily_total = morning_conditions + afternoon_conditions\n", "print(f\"\\n🌅 Morning: {dict(morning_conditions)}\")\n", "print(f\"🌇 Afternoon: {dict(afternoon_conditions)}\")\n", "print(f\"📊 Daily total: {dict(daily_total)}\")\n", "\n", "# Find difference\n", "condition_change = afternoon_conditions - morning_conditions\n", "print(f\"📈 Afternoon increase: {dict(condition_change)}\")\n", "\n", "# Temperature range analysis\n", "temperatures = [22, 23, 21, 24, 22, 25, 23, 22, 26, 24, 23, 22]\n", "temp_distribution = Counter(temperatures)\n", "\n", "print(f\"\\n🌡️ Temperature distribution:\")\n", "for temp in sorted(temp_distribution.keys()):\n", "    count = temp_distribution[temp]\n", "    bar = '█' * count\n", "    print(f\"   {temp}°C: {bar} ({count})\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# deque - Double-ended queue, efficient for sliding windows\n", "print(\"⚡ deque - Efficient Sliding Windows:\")\n", "\n", "# Sliding window for temperature moving average\n", "from collections import deque\n", "\n", "# Temperature readings over time\n", "temp_stream = [20, 22, 21, 23, 25, 24, 26, 23, 21, 19, 18, 20, 22, 24]\n", "window_size = 5\n", "\n", "# Sliding window with deque\n", "window = deque(maxlen=window_size)\n", "moving_averages = []\n", "\n", "print(f\"\\n📊 5-point moving average:\")\n", "for i, temp in enumerate(temp_stream):\n", "    window.append(temp)\n", "    \n", "    if len(window) == window_size:\n", "        avg = sum(window) / len(window)\n", "        moving_averages.append(avg)\n", "        print(f\"   Time {i+1:2d}: {temp}°C → avg({list(window)}) = {avg:.1f}°C\")\n", "    else:\n", "        print(f\"   Time {i+1:2d}: {temp}°C → warming up... ({len(window)}/{window_size})\")\n", "\n", "# Efficient queue operations\n", "print(f\"\\n🔄 Deque Operations:\")\n", "data_queue = deque(['old_data_1', 'old_data_2', 'old_data_3'])\n", "print(f\"   Initial queue: {list(data_queue)}\")\n", "\n", "# Add to both ends\n", "data_queue.appendleft('newest_data')  # Add to front\n", "data_queue.append('latest_data')      # Add to back\n", "print(f\"   After additions: {list(data_queue)}\")\n", "\n", "# Remove from both ends\n", "newest = data_queue.popleft()  # Remove from front\n", "latest = data_queue.pop()      # Remove from back\n", "print(f\"   Removed: {newest} (front), {latest} (back)\")\n", "print(f\"   Final queue: {list(data_queue)}\")\n", "\n", "# Real-time data buffer example\n", "class WeatherDataBuffer:\n", "    \"\"\"Efficient buffer for real-time weather data.\"\"\"\n", "    \n", "    def __init__(self, max_size=100):\n", "        self.buffer = deque(maxlen=max_size)\n", "        self.max_size = max_size\n", "    \n", "    def add_reading(self, reading):\n", "        \"\"\"Add new reading, automatically removes oldest if full.\"\"\"\n", "        self.buffer.append(reading)\n", "    \n", "    def get_recent(self, n=10):\n", "        \"\"\"Get n most recent readings.\"\"\"\n", "        return list(self.buffer)[-n:]\n", "    \n", "    def get_average_temp(self):\n", "        \"\"\"Calculate average temperature from buffer.\"\"\"\n", "        if not self.buffer:\n", "            return None\n", "        temps = [r['temp'] for r in self.buffer if 'temp' in r]\n", "        return sum(temps) / len(temps) if temps else None\n", "\n", "# Test the buffer\n", "buffer = WeatherDataBuffer(max_size=5)\n", "test_readings = [\n", "    {'temp': 20, 'time': '10:00'},\n", "    {'temp': 22, 'time': '11:00'},\n", "    {'temp': 24, 'time': '12:00'},\n", "    {'temp': 23, 'time': '13:00'},\n", "    {'temp': 21, 'time': '14:00'},\n", "    {'temp': 19, 'time': '15:00'},  # This will push out the first reading\n", "]\n", "\n", "print(f\"\\n📡 Real-time Buffer Demo:\")\n", "for reading in test_readings:\n", "    buffer.add_reading(reading)\n", "    avg = buffer.get_average_temp()\n", "    recent = buffer.get_recent(3)\n", "    print(f\"   Added {reading} → avg: {avg:.1f}°C, recent 3: {[r['temp'] for r in recent]}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---\n", "\n", "# 🏗️ **Section 3: Object-Oriented Programming**\n", "\n", "Master the OOP patterns used extensively in WeatherBench2.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Basic Classes - Foundation of OOP\n", "print(\"🏗️ Basic Classes - WeatherBench2 Style:\")\n", "\n", "class WeatherStation:\n", "    \"\"\"Represents a weather monitoring station.\"\"\"\n", "    \n", "    # Class variable - shared by all instances\n", "    total_stations = 0\n", "    \n", "    def __init__(self, station_id: str, latitude: float, longitude: float, elevation: float = 0):\n", "        \"\"\"Initialize weather station.\"\"\"\n", "        self.station_id = station_id\n", "        self.latitude = latitude\n", "        self.longitude = longitude\n", "        self.elevation = elevation\n", "        self.readings = []  # Instance variable\n", "        \n", "        # Update class variable\n", "        WeatherStation.total_stations += 1\n", "    \n", "    def add_reading(self, temperature: float, humidity: float, pressure: float, timestamp: str = None):\n", "        \"\"\"Add a weather reading.\"\"\"\n", "        if timestamp is None:\n", "            timestamp = datetime.now().isoformat()\n", "        \n", "        reading = {\n", "            'temperature': temperature,\n", "            'humidity': humidity,\n", "            'pressure': pressure,\n", "            'timestamp': timestamp\n", "        }\n", "        self.readings.append(reading)\n", "    \n", "    def get_current_temperature(self) -> Optional[float]:\n", "        \"\"\"Get most recent temperature reading.\"\"\"\n", "        if not self.readings:\n", "            return None\n", "        return self.readings[-1]['temperature']\n", "    \n", "    def get_average_temperature(self) -> Optional[float]:\n", "        \"\"\"Calculate average temperature from all readings.\"\"\"\n", "        if not self.readings:\n", "            return None\n", "        \n", "        temps = [r['temperature'] for r in self.readings]\n", "        return sum(temps) / len(temps)\n", "    \n", "    def is_online(self, max_hours_since_reading: int = 2) -> bool:\n", "        \"\"\"Check if station is online based on recent readings.\"\"\"\n", "        if not self.readings:\n", "            return False\n", "        \n", "        # For demo, just check if we have readings\n", "        return len(self.readings) > 0\n", "    \n", "    def __str__(self) -> str:\n", "        \"\"\"String representation.\"\"\"\n", "        current_temp = self.get_current_temperature()\n", "        temp_str = f\"{current_temp:.1f}°C\" if current_temp else \"No data\"\n", "        return f\"WeatherStation({self.station_id}: {temp_str})\"\n", "    \n", "    def __repr__(self) -> str:\n", "        \"\"\"Developer representation.\"\"\"\n", "        return f\"WeatherStation('{self.station_id}', {self.latitude}, {self.longitude})\"\n", "\n", "# Create and test weather stations\n", "nyc_station = WeatherStation('NYC001', 40.7128, -74.0060, 10)\n", "la_station = WeatherStation('LA001', 34.0522, -118.2437, 71)\n", "\n", "print(f\"\\n🏙️ Created stations:\")\n", "print(f\"   {nyc_station}\")\n", "print(f\"   {la_station}\")\n", "print(f\"   Total stations: {WeatherStation.total_stations}\")\n", "\n", "# Add some readings\n", "nyc_station.add_reading(22.5, 65, 1013.2)\n", "nyc_station.add_reading(23.1, 68, 1012.8)\n", "nyc_station.add_reading(21.8, 70, 1014.1)\n", "\n", "la_station.add_reading(28.1, 45, 1015.8)\n", "la_station.add_reading(29.2, 42, 1016.2)\n", "\n", "print(f\"\\n📊 Station Statistics:\")\n", "print(f\"   NYC: {len(nyc_station.readings)} readings, avg {nyc_station.get_average_temperature():.1f}°C\")\n", "print(f\"   LA: {len(la_station.readings)} readings, avg {la_station.get_average_temperature():.1f}°C\")\n", "print(f\"   NYC online: {nyc_station.is_online()}\")\n", "print(f\"   LA online: {la_station.is_online()}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Dataclasses - Modern Python way (used extensively in WeatherBench2)\n", "print(\"📦 Dataclasses - Modern Python (WeatherBench2 Pattern):\")\n", "\n", "@dataclasses.dataclass\n", "class WeatherReading:\n", "    \"\"\"Immutable weather reading - WeatherBench2 style.\"\"\"\n", "    station_id: str\n", "    temperature: float\n", "    humidity: float\n", "    pressure: float\n", "    timestamp: str\n", "    \n", "    def to_celsius(self) -> float:\n", "        \"\"\"Temper<PERSON> is already in Celsius.\"\"\"\n", "        return self.temperature\n", "    \n", "    def to_fahrenheit(self) -> float:\n", "        \"\"\"Convert temperature to Fahrenheit.\"\"\"\n", "        return self.temperature * 9/5 + 32\n", "\n", "@dataclasses.dataclass\n", "class StationConfig:\n", "    \"\"\"Configuration for weather station - similar to WeatherBench2 config pattern.\"\"\"\n", "    station_id: str\n", "    latitude: float\n", "    longitude: float\n", "    elevation: float = 0.0\n", "    active: bool = True\n", "    variables: List[str] = dataclasses.field(default_factory=lambda: ['temperature', 'humidity', 'pressure'])\n", "    metadata: Dict[str, Any] = dataclasses.field(default_factory=dict)\n", "    \n", "    def __post_init__(self):\n", "        \"\"\"Validation after initialization.\"\"\"\n", "        if not (-90 <= self.latitude <= 90):\n", "            raise ValueError(f\"Invalid latitude: {self.latitude}\")\n", "        if not (-180 <= self.longitude <= 180):\n", "            raise ValueError(f\"Invalid longitude: {self.longitude}\")\n", "\n", "# Create dataclass instances\n", "reading1 = WeatherReading(\n", "    station_id='NYC001',\n", "    temperature=22.5,\n", "    humidity=65.0,\n", "    pressure=1013.2,\n", "    timestamp='2023-01-01T12:00:00'\n", ")\n", "\n", "config1 = StationConfig(\n", "    station_id='NYC001',\n", "    latitude=40.7128,\n", "    longitude=-74.0060,\n", "    elevation=10.0,\n", "    metadata={'city': 'New York', 'country': 'USA'}\n", ")\n", "\n", "print(f\"\\n📊 Weather Reading:\")\n", "print(f\"   {reading1}\")\n", "print(f\"   Temperature: {reading1.to_celsius():.1f}°C / {reading1.to_fahrenheit():.1f}°F\")\n", "\n", "print(f\"\\n⚙️ Station Config:\")\n", "print(f\"   {config1}\")\n", "print(f\"   Variables: {config1.variables}\")\n", "print(f\"   Metadata: {config1.metadata}\")\n", "\n", "# Dataclass features\n", "reading2 = WeatherReading('LA001', 28.1, 45.0, 1015.8, '2023-01-01T12:00:00')\n", "readings = [reading1, reading2]\n", "\n", "# Automatic comparison\n", "print(f\"\\n🔍 Dataclass Features:\")\n", "print(f\"   Readings equal: {reading1 == reading2}\")\n", "print(f\"   Reading1 hash: {hash(reading1) if reading1.__hash__ else 'Not hashable'}\")\n", "\n", "# Convert to dictionary\n", "reading_dict = dataclasses.asdict(reading1)\n", "print(f\"   As dict: {reading_dict}\")\n", "\n", "# Replace (immutable update)\n", "updated_reading = dataclasses.replace(reading1, temperature=23.0)\n", "print(f\"   Original temp: {reading1.temperature}°C\")\n", "print(f\"   Updated temp: {updated_reading.temperature}°C\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Inheritance and Abstract Base Classes - WeatherBench2 pattern\n", "print(\"🏛️ Inheritance & Abstract Classes (WeatherBench2 Pattern):\")\n", "\n", "# Abstract base class - like WeatherBench2's Metric class\n", "class WeatherProcessor(ABC):\n", "    \"\"\"Abstract base class for weather data processors.\"\"\"\n", "    \n", "    def __init__(self, name: str):\n", "        self.name = name\n", "    \n", "    @abstractmethod\n", "    def process(self, data: List[Dict]) -> Dict:\n", "        \"\"\"Process weather data - must be implemented by subclasses.\"\"\"\n", "        pass\n", "    \n", "    @property\n", "    @abstractmethod\n", "    def required_variables(self) -> List[str]:\n", "        \"\"\"Variables required for processing.\"\"\"\n", "        pass\n", "    \n", "    def validate_data(self, data: List[Dict]) -> bool:\n", "        \"\"\"Common validation logic.\"\"\"\n", "        if not data:\n", "            return False\n", "        \n", "        # Check if all required variables are present\n", "        for record in data[:5]:  # Check first 5 records\n", "            for var in self.required_variables:\n", "                if var not in record:\n", "                    print(f\"⚠️ Missing variable '{var}' in data\")\n", "                    return False\n", "        return True\n", "\n", "# Concrete implementation - Temperature analyzer\n", "class TemperatureAnalyzer(WeatherProcessor):\n", "    \"\"\"Analyzes temperature data.\"\"\"\n", "    \n", "    def __init__(self):\n", "        super().__init__(\"Temperature Analyzer\")\n", "    \n", "    @property\n", "    def required_variables(self) -> List[str]:\n", "        return ['temperature']\n", "    \n", "    def process(self, data: List[Dict]) -> Dict:\n", "        \"\"\"Process temperature data.\"\"\"\n", "        if not self.validate_data(data):\n", "            return {'error': 'Invalid data'}\n", "        \n", "        temperatures = [record['temperature'] for record in data]\n", "        \n", "        return {\n", "            'mean': sum(temperatures) / len(temperatures),\n", "            'min': min(temperatures),\n", "            'max': max(temperatures),\n", "            'range': max(temperatures) - min(temperatures),\n", "            'count': len(temperatures)\n", "        }\n", "\n", "# Another concrete implementation - Humidity analyzer\n", "class HumidityAnalyzer(WeatherProcessor):\n", "    \"\"\"Analyzes humidity data.\"\"\"\n", "    \n", "    def __init__(self, comfort_range: Tuple[float, float] = (40.0, 60.0)):\n", "        super().__init__(\"Humidity Analyzer\")\n", "        self.comfort_range = comfort_range\n", "    \n", "    @property\n", "    def required_variables(self) -> List[str]:\n", "        return ['humidity']\n", "    \n", "    def process(self, data: List[Dict]) -> Dict:\n", "        \"\"\"Process humidity data.\"\"\"\n", "        if not self.validate_data(data):\n", "            return {'error': 'Invalid data'}\n", "        \n", "        humidities = [record['humidity'] for record in data]\n", "        \n", "        # Comfort analysis\n", "        comfortable_readings = [\n", "            h for h in humidities \n", "            if self.comfort_range[0] <= h <= self.comfort_range[1]\n", "        ]\n", "        \n", "        return {\n", "            'mean': sum(humidities) / len(humidities),\n", "            'min': min(humidities),\n", "            'max': max(humidities),\n", "            'comfortable_readings': len(comfortable_readings),\n", "            'comfort_percentage': len(comfortable_readings) / len(humidities) * 100,\n", "            'count': len(humidities)\n", "        }\n", "\n", "# Test the inheritance pattern\n", "sample_data = [\n", "    {'temperature': 22.5, 'humidity': 65.0, 'pressure': 1013.2},\n", "    {'temperature': 23.1, 'humidity': 68.0, 'pressure': 1012.8},\n", "    {'temperature': 21.8, 'humidity': 70.0, 'pressure': 1014.1},\n", "    {'temperature': 24.2, 'humidity': 55.0, 'pressure': 1015.5},\n", "    {'temperature': 23.8, 'humidity': 58.0, 'pressure': 1013.9}\n", "]\n", "\n", "# Create processors\n", "temp_analyzer = TemperatureAnalyzer()\n", "humidity_analyzer = HumidityAnalyzer()\n", "\n", "processors = [temp_analyzer, humidity_analyzer]\n", "\n", "print(f\"\\n📊 Processing Results:\")\n", "for processor in processors:\n", "    result = processor.process(sample_data)\n", "    print(f\"\\n   {processor.name}:\")\n", "    for key, value in result.items():\n", "        if isinstance(value, float):\n", "            print(f\"      {key}: {value:.2f}\")\n", "        else:\n", "            print(f\"      {key}: {value}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Protocols and Type Hints - Modern Python typing\n", "print(\"🔤 Protocols & Type Hints (Modern Python):\")\n", "\n", "# Protocol - defines interface without inheritance\n", "class Processable(Protocol):\n", "    \"\"\"Protocol for objects that can be processed.\"\"\"\n", "    \n", "    def get_data(self) -> Dict[str, float]:\n", "        \"\"\"Get data for processing.\"\"\"\n", "        ...\n", "    \n", "    def get_timestamp(self) -> str:\n", "        \"\"\"Get timestamp.\"\"\"\n", "        ...\n", "\n", "# Classes that implement the protocol (duck typing)\n", "class SensorReading:\n", "    \"\"\"Sensor reading that implements Processable protocol.\"\"\"\n", "    \n", "    def __init__(self, temp: float, humidity: float, timestamp: str):\n", "        self.temp = temp\n", "        self.humidity = humidity\n", "        self.timestamp = timestamp\n", "    \n", "    def get_data(self) -> Dict[str, float]:\n", "        return {'temperature': self.temp, 'humidity': self.humidity}\n", "    \n", "    def get_timestamp(self) -> str:\n", "        return self.timestamp\n", "\n", "class WeatherObservation:\n", "    \"\"\"Weather observation that also implements Processable protocol.\"\"\"\n", "    \n", "    def __init__(self, data: Dict[str, float], time: str):\n", "        self.data = data\n", "        self.time = time\n", "    \n", "    def get_data(self) -> Dict[str, float]:\n", "        return self.data\n", "    \n", "    def get_timestamp(self) -> str:\n", "        return self.time\n", "\n", "# Function that works with any Processable object\n", "def process_readings(readings: List[Processable]) -> Dict[str, Any]:\n", "    \"\"\"Process any objects that implement Processable protocol.\"\"\"\n", "    all_data = []\n", "    timestamps = []\n", "    \n", "    for reading in readings:\n", "        data = reading.get_data()\n", "        timestamp = reading.get_timestamp()\n", "        \n", "        all_data.append(data)\n", "        timestamps.append(timestamp)\n", "    \n", "    # Calculate averages\n", "    if all_data:\n", "        # Get all variable names\n", "        all_vars = set()\n", "        for data in all_data:\n", "            all_vars.update(data.keys())\n", "        \n", "        averages = {}\n", "        for var in all_vars:\n", "            values = [data.get(var, 0) for data in all_data if var in data]\n", "            if values:\n", "                averages[f'avg_{var}'] = sum(values) / len(values)\n", "        \n", "        return {\n", "            'count': len(all_data),\n", "            'time_range': f\"{min(timestamps)} to {max(timestamps)}\",\n", "            **averages\n", "        }\n", "    \n", "    return {'count': 0}\n", "\n", "# Test protocol usage\n", "sensor1 = SensorReading(22.5, 65.0, '2023-01-01T12:00')\n", "sensor2 = SensorReading(23.1, 68.0, '2023-01-01T13:00')\n", "\n", "obs1 = WeatherObservation(\n", "    {'temperature': 21.8, 'humidity': 70.0, 'pressure': 1014.1},\n", "    '2023-01-01T14:00'\n", ")\n", "obs2 = WeatherObservation(\n", "    {'temperature': 24.2, 'humidity': 55.0, 'pressure': 1015.5},\n", "    '2023-01-01T15:00'\n", ")\n", "\n", "# Mix different types that implement the same protocol\n", "mixed_readings = [sensor1, sensor2, obs1, obs2]\n", "result = process_readings(mixed_readings)\n", "\n", "print(f\"\\n📊 Protocol Processing Results:\")\n", "for key, value in result.items():\n", "    if isinstance(value, float):\n", "        print(f\"   {key}: {value:.2f}\")\n", "    else:\n", "        print(f\"   {key}: {value}\")\n", "\n", "print(f\"\\n✅ Protocol allows different classes to work together seamlessly!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---\n", "\n", "# ⚡ **Section 4: Functional Programming & Performance**\n", "\n", "Advanced Python patterns for efficient and elegant code.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Generators and Iterators - Memory efficient data processing\n", "print(\"🔄 Generators & Iterators - Memory Efficient Processing:\")\n", "\n", "def weather_data_generator(start_temp: float = 20.0, days: int = 365):\n", "    \"\"\"Generate weather data efficiently - one reading at a time.\"\"\"\n", "    current_time = datetime(2023, 1, 1)\n", "    \n", "    for day in range(days):\n", "        # Seasonal temperature variation\n", "        seasonal_temp = start_temp + 10 * np.sin(2 * np.pi * day / 365)\n", "        \n", "        for hour in range(24):\n", "            # Daily temperature variation\n", "            daily_temp = seasonal_temp + 5 * np.sin(2 * np.pi * hour / 24)\n", "            \n", "            # Add realistic noise\n", "            temp = daily_temp + np.random.normal(0, 2)\n", "            humidity = max(0, min(100, 50 + np.random.normal(0, 15)))\n", "            pressure = 1013 + np.random.normal(0, 10)\n", "            \n", "            yield {\n", "                'timestamp': current_time.isoformat(),\n", "                'temperature': round(temp, 1),\n", "                'humidity': round(humidity, 1),\n", "                'pressure': round(pressure, 1)\n", "            }\n", "            \n", "            current_time += <PERSON>elta(hours=1)\n", "\n", "# Generator expressions - even more concise\n", "def process_weather_stream(data_generator, temp_threshold: float = 25.0):\n", "    \"\"\"Process weather data stream efficiently.\"\"\"\n", "    \n", "    # Generator expression for filtering\n", "    hot_days = (reading for reading in data_generator if reading['temperature'] > temp_threshold)\n", "    \n", "    # Process only what we need\n", "    hot_readings = []\n", "    for i, reading in enumerate(hot_days):\n", "        hot_readings.append(reading)\n", "        if i >= 9:  # Only take first 10 hot readings\n", "            break\n", "    \n", "    return hot_readings\n", "\n", "# Test generator efficiency\n", "print(f\"\\n🔥 Generator Demo:\")\n", "weather_gen = weather_data_generator(start_temp=20.0, days=7)  # One week\n", "\n", "# Take first 5 readings\n", "first_readings = [next(weather_gen) for _ in range(5)]\n", "print(f\"   First 5 readings generated:\")\n", "for i, reading in enumerate(first_readings):\n", "    print(f\"      {i+1}: {reading['temperature']}°C at {reading['timestamp'][:16]}\")\n", "\n", "# Process hot days efficiently\n", "weather_gen = weather_data_generator(start_temp=25.0, days=30)  # One month, warmer\n", "hot_readings = process_weather_stream(weather_gen, temp_threshold=30.0)\n", "\n", "print(f\"\\n🌡️ Hot readings (>30°C):\")\n", "print(f\"   Found {len(hot_readings)} hot readings\")\n", "if hot_readings:\n", "    avg_hot_temp = sum(r['temperature'] for r in hot_readings) / len(hot_readings)\n", "    print(f\"   Average hot temperature: {avg_hot_temp:.1f}°C\")\n", "\n", "# Iterator protocol example\n", "class WeatherDataIterator:\n", "    \"\"\"Custom iterator for weather data.\"\"\"\n", "    \n", "    def __init__(self, data: List[Dict], batch_size: int = 3):\n", "        self.data = data\n", "        self.batch_size = batch_size\n", "        self.index = 0\n", "    \n", "    def __iter__(self):\n", "        return self\n", "    \n", "    def __next__(self):\n", "        if self.index >= len(self.data):\n", "            raise StopIteration\n", "        \n", "        # Return batch of data\n", "        batch = self.data[self.index:self.index + self.batch_size]\n", "        self.index += self.batch_size\n", "        return batch\n", "\n", "# Test custom iterator\n", "sample_data = [\n", "    {'temp': 20, 'time': '10:00'}, {'temp': 22, 'time': '11:00'},\n", "    {'temp': 24, 'time': '12:00'}, {'temp': 23, 'time': '13:00'},\n", "    {'temp': 21, 'time': '14:00'}, {'temp': 19, 'time': '15:00'},\n", "    {'temp': 18, 'time': '16:00'}\n", "]\n", "\n", "print(f\"\\n📦 Custom Iterator (batches of 3):\")\n", "batch_iterator = WeatherDataIterator(sample_data, batch_size=3)\n", "for i, batch in enumerate(batch_iterator):\n", "    temps = [r['temp'] for r in batch]\n", "    print(f\"   Batch {i+1}: {temps}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Decorators - Function enhancement patterns\n", "print(\"🎭 Decorators - Function Enhancement:\")\n", "\n", "# Timing decorator - measure performance\n", "def timing_decorator(func):\n", "    \"\"\"Decorator to measure function execution time.\"\"\"\n", "    @functools.wraps(func)\n", "    def wrapper(*args, **kwargs):\n", "        start_time = time.time()\n", "        result = func(*args, **kwargs)\n", "        end_time = time.time()\n", "        print(f\"⏱️ {func.__name__} took {end_time - start_time:.4f} seconds\")\n", "        return result\n", "    return wrapper\n", "\n", "# Caching decorator - avoid repeated calculations\n", "def simple_cache(func):\n", "    \"\"\"Simple caching decorator.\"\"\"\n", "    cache = {}\n", "    \n", "    @functools.wraps(func)\n", "    def wrapper(*args, **kwargs):\n", "        # Create cache key\n", "        key = str(args) + str(sorted(kwargs.items()))\n", "        \n", "        if key in cache:\n", "            print(f\"💾 Cache hit for {func.__name__}\")\n", "            return cache[key]\n", "        \n", "        result = func(*args, **kwargs)\n", "        cache[key] = result\n", "        print(f\"🔄 Computed and cached {func.__name__}\")\n", "        return result\n", "    \n", "    return wrapper\n", "\n", "# Validation decorator\n", "def validate_temperature_range(min_temp=-50, max_temp=60):\n", "    \"\"\"Decorator to validate temperature inputs.\"\"\"\n", "    def decorator(func):\n", "        @functools.wraps(func)\n", "        def wrapper(*args, **kwargs):\n", "            # Check for temperature in args or kwargs\n", "            temp = None\n", "            if args and isinstance(args[0], (int, float)):\n", "                temp = args[0]\n", "            elif 'temperature' in kwargs:\n", "                temp = kwargs['temperature']\n", "            \n", "            if temp is not None and not (min_temp <= temp <= max_temp):\n", "                raise ValueError(f\"Temperature {temp}°C outside valid range [{min_temp}, {max_temp}]\")\n", "            \n", "            return func(*args, **kwargs)\n", "        return wrapper\n", "    return decorator\n", "\n", "# Apply decorators to functions\n", "@timing_decorator\n", "@simple_cache\n", "def expensive_weather_calculation(temperature: float, humidity: float) -> float:\n", "    \"\"\"Simulate expensive calculation.\"\"\"\n", "    time.sleep(0.1)  # Simulate work\n", "    # Heat index calculation\n", "    return temperature + (humidity / 100) * 5\n", "\n", "@validate_temperature_range(min_temp=-40, max_temp=50)\n", "def convert_to_fahrenheit(temperature: float) -> float:\n", "    \"\"\"Convert <PERSON><PERSON><PERSON> to Fahrenheit with validation.\"\"\"\n", "    return temperature * 9/5 + 32\n", "\n", "# Test decorators\n", "print(f\"\\n🧮 Testing Decorators:\")\n", "\n", "# Test caching\n", "result1 = expensive_weather_calculation(25.0, 60.0)\n", "result2 = expensive_weather_calculation(25.0, 60.0)  # Should use cache\n", "result3 = expensive_weather_calculation(30.0, 70.0)  # New calculation\n", "\n", "print(f\"   Results: {result1:.1f}, {result2:.1f}, {result3:.1f}\")\n", "\n", "# Test validation\n", "try:\n", "    valid_temp = convert_to_fahrenheit(25.0)\n", "    print(f\"   25°C = {valid_temp:.1f}°F\")\n", "    \n", "    invalid_temp = convert_to_fahrenheit(100.0)  # Should raise error\n", "except ValueError as e:\n", "    print(f\"   ❌ Validation error: {e}\")\n", "\n", "# Built-in decorators\n", "class WeatherAnalyzer:\n", "    \"\"\"Example using property decorators.\"\"\"\n", "    \n", "    def __init__(self):\n", "        self._temperatures = []\n", "    \n", "    @property\n", "    def temperatures(self) -> List[float]:\n", "        \"\"\"Get temperature readings.\"\"\"\n", "        return self._temperatures.copy()\n", "    \n", "    @temperatures.setter\n", "    def temperatures(self, values: List[float]):\n", "        \"\"\"Set temperature readings with validation.\"\"\"\n", "        if not all(-50 <= temp <= 60 for temp in values):\n", "            raise ValueError(\"Invalid temperature range\")\n", "        self._temperatures = values.copy()\n", "    \n", "    @property\n", "    def average_temperature(self) -> Optional[float]:\n", "        \"\"\"Calculate average temperature.\"\"\"\n", "        if not self._temperatures:\n", "            return None\n", "        return sum(self._temperatures) / len(self._temperatures)\n", "    \n", "    @staticmethod\n", "    def celsius_to_kelvin(celsius: float) -> float:\n", "        \"\"\"Convert <PERSON><PERSON><PERSON> to <PERSON><PERSON>.\"\"\"\n", "        return celsius + 273.15\n", "    \n", "    @classmethod\n", "    def from_fahrenheit_list(cls, fahrenheit_temps: List[float]):\n", "        \"\"\"Create analyzer from Fahrenheit temperatures.\"\"\"\n", "        celsius_temps = [(f - 32) * 5/9 for f in fahrenheit_temps]\n", "        analyzer = cls()\n", "        analyzer.temperatures = celsius_temps\n", "        return analyzer\n", "\n", "# Test property decorators\n", "analyzer = WeatherAnalyzer()\n", "analyzer.temperatures = [20.0, 22.5, 21.8, 23.1, 19.5]\n", "\n", "print(f\"\\n📊 Property Decorators:\")\n", "print(f\"   Temperatures: {analyzer.temperatures}\")\n", "print(f\"   Average: {analyzer.average_temperature:.1f}°C\")\n", "print(f\"   25°C in Kelvin: {WeatherAnalyzer.celsius_to_kelvin(25.0):.1f}K\")\n", "\n", "# Class method example\n", "fahrenheit_analyzer = WeatherAnalyzer.from_fahrenheit_list([68.0, 72.5, 71.2, 73.6, 67.1])\n", "print(f\"   From Fahrenheit: avg {fahrenheit_analyzer.average_temperature:.1f}°C\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---\n", "\n", "# 🛡️ **Section 5: <PERSON><PERSON>r Handling & Robust Code**\n", "\n", "Essential patterns for writing reliable weather data processing code.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Exception handling patterns\n", "print(\"🛡️ Exception Handling Patterns:\")\n", "\n", "class WeatherDataError(Exception):\n", "    \"\"\"Custom exception for weather data issues.\"\"\"\n", "    pass\n", "\n", "class InvalidTemperatureError(WeatherDataError):\n", "    \"\"\"Raised when temperature is outside valid range.\"\"\"\n", "    pass\n", "\n", "class MissingDataError(WeatherDataError):\n", "    \"\"\"Raised when required data is missing.\"\"\"\n", "    pass\n", "\n", "def parse_weather_reading(raw_data: str) -> Dict[str, float]:\n", "    \"\"\"Parse weather reading with comprehensive error handling.\"\"\"\n", "    try:\n", "        # Expected format: \"station_id,temperature,humidity,pressure,timestamp\"\n", "        parts = raw_data.strip().split(',')\n", "        \n", "        if len(parts) != 5:\n", "            raise ValueError(f\"Expected 5 fields, got {len(parts)}\")\n", "        \n", "        station_id, temp_str, humidity_str, pressure_str, timestamp = parts\n", "        \n", "        # Parse numeric values\n", "        temperature = float(temp_str)\n", "        humidity = float(humidity_str)\n", "        pressure = float(pressure_str)\n", "        \n", "        # Validate ranges\n", "        if not (-50 <= temperature <= 60):\n", "            raise InvalidTemperatureError(f\"Temperature {temperature}°C outside valid range\")\n", "        \n", "        if not (0 <= humidity <= 100):\n", "            raise ValueError(f\"Humidity {humidity}% outside valid range\")\n", "        \n", "        if not (800 <= pressure <= 1200):\n", "            raise ValueError(f\"Pressure {pressure} hPa outside valid range\")\n", "        \n", "        return {\n", "            'station_id': station_id,\n", "            'temperature': temperature,\n", "            'humidity': humidity,\n", "            'pressure': pressure,\n", "            'timestamp': timestamp\n", "        }\n", "    \n", "    except ValueError as e:\n", "        raise WeatherDataError(f\"Failed to parse weather data: {e}\") from e\n", "    except Exception as e:\n", "        raise WeatherDataError(f\"Unexpected error parsing weather data: {e}\") from e\n", "\n", "def process_weather_file(file_content: List[str]) -> Dict[str, Any]:\n", "    \"\"\"Process weather file with error recovery.\"\"\"\n", "    valid_readings = []\n", "    errors = []\n", "    \n", "    for line_num, line in enumerate(file_content, 1):\n", "        try:\n", "            reading = parse_weather_reading(line)\n", "            valid_readings.append(reading)\n", "        except WeatherDataError as e:\n", "            error_info = {\n", "                'line_number': line_num,\n", "                'line_content': line.strip(),\n", "                'error': str(e)\n", "            }\n", "            errors.append(error_info)\n", "            continue  # Skip invalid readings but continue processing\n", "    \n", "    return {\n", "        'valid_readings': valid_readings,\n", "        'errors': errors,\n", "        'success_rate': len(valid_readings) / len(file_content) if file_content else 0\n", "    }\n", "\n", "# Test error handling\n", "sample_weather_data = [\n", "    \"NYC001,22.5,65,1013.2,2023-01-01T12:00\",  # Valid\n", "    \"LA001,28.1,45,1015.8,2023-01-01T12:00\",   # Valid\n", "    \"CHI001,invalid,68,1012.8,2023-01-01T12:00\",  # Invalid temperature\n", "    \"HOU001,31.2,150,1014.5,2023-01-01T12:00\",  # Invalid humidity\n", "    \"PHX001,35.8,42\",  # Missing fields\n", "    \"DEN001,19.3,55,1020.1,2023-01-01T12:00\",  # Valid\n", "]\n", "\n", "print(f\"\\n📊 Processing Weather File:\")\n", "result = process_weather_file(sample_weather_data)\n", "\n", "print(f\"   Valid readings: {len(result['valid_readings'])}\")\n", "print(f\"   Errors: {len(result['errors'])}\")\n", "print(f\"   Success rate: {result['success_rate']:.1%}\")\n", "\n", "print(f\"\\n❌ Error Details:\")\n", "for error in result['errors']:\n", "    print(f\"   Line {error['line_number']}: {error['error']}\")\n", "\n", "# Context managers for resource management\n", "print(f\"\\n🔧 Context Managers:\")\n", "\n", "class WeatherDataProcessor:\n", "    \"\"\"Context manager for weather data processing.\"\"\"\n", "    \n", "    def __init__(self, name: str):\n", "        self.name = name\n", "        self.start_time = None\n", "        self.processed_count = 0\n", "    \n", "    def __enter__(self):\n", "        print(f\"🚀 Starting {self.name} processing...\")\n", "        self.start_time = time.time()\n", "        return self\n", "    \n", "    def __exit__(self, exc_type, exc_val, exc_tb):\n", "        duration = time.time() - self.start_time\n", "        \n", "        if exc_type is None:\n", "            print(f\"✅ {self.name} completed successfully\")\n", "            print(f\"   Processed {self.processed_count} items in {duration:.2f}s\")\n", "        else:\n", "            print(f\"❌ {self.name} failed with {exc_type.__name__}: {exc_val}\")\n", "            print(f\"   Processed {self.processed_count} items before failure\")\n", "        \n", "        return False  # Don't suppress exceptions\n", "    \n", "    def process_item(self, item):\n", "        \"\"\"Process a single item.\"\"\"\n", "        # Simulate processing\n", "        time.sleep(0.01)\n", "        self.processed_count += 1\n", "        return f\"processed_{item}\"\n", "\n", "# Test context manager\n", "try:\n", "    with WeatherDataProcessor(\"Temperature Analysis\") as processor:\n", "        for i in range(5):\n", "            result = processor.process_item(f\"reading_{i}\")\n", "except Exception as e:\n", "    print(f\"Caught exception: {e}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---\n", "\n", "# 🌤️ **Section 6: WeatherBench2 Specific Patterns**\n", "\n", "Real patterns and practices from the WeatherBench2 codebase.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# WeatherBench2 Configuration Pattern\n", "print(\"⚙️ WeatherBench2 Configuration Patterns:\")\n", "\n", "# Based on weatherbench2/config.py patterns\n", "@dataclasses.dataclass\n", "class Selection:\n", "    \"\"\"Data selection configuration - WeatherBench2 style.\"\"\"\n", "    variables: List[str]\n", "    levels: Optional[List[int]] = None\n", "    time_slice: Optional[slice] = None\n", "    \n", "    def __post_init__(self):\n", "        if not self.variables:\n", "            raise ValueError(\"Variables list cannot be empty\")\n", "\n", "@dataclasses.dataclass\n", "class Paths:\n", "    \"\"\"File path configuration.\"\"\"\n", "    forecast: str\n", "    observations: str\n", "    output: str\n", "\n", "@dataclasses.dataclass\n", "class EvaluationConfig:\n", "    \"\"\"Complete evaluation configuration - WeatherBench2 pattern.\"\"\"\n", "    selection: Selection\n", "    paths: Paths\n", "    metrics: Dict[str, str] = dataclasses.field(default_factory=dict)\n", "    regions: Dict[str, str] = dataclasses.field(default_factory=dict)\n", "    \n", "    def validate(self) -> bool:\n", "        \"\"\"Validate configuration.\"\"\"\n", "        if not self.metrics:\n", "            print(\"⚠️ No metrics specified\")\n", "            return False\n", "        return True\n", "\n", "# WeatherBench2 <PERSON><PERSON> (Abstract Base Class)\n", "class Metric(ABC):\n", "    \"\"\"Abstract base class for metrics - WeatherBench2 pattern.\"\"\"\n", "    \n", "    def __init__(self, name: str):\n", "        self.name = name\n", "    \n", "    @abstractmethod\n", "    def compute(self, forecast: np.ndarray, observations: np.ndarray) -> float:\n", "        \"\"\"Compute metric value.\"\"\"\n", "        pass\n", "    \n", "    @property\n", "    @abstractmethod\n", "    def units(self) -> str:\n", "        \"\"\"Metric units.\"\"\"\n", "        pass\n", "\n", "class RMSE(Metric):\n", "    \"\"\"Root Mean Square Error metric.\"\"\"\n", "    \n", "    def __init__(self):\n", "        super().__init__(\"RMSE\")\n", "    \n", "    def compute(self, forecast: np.ndarray, observations: np.ndarray) -> float:\n", "        \"\"\"Compute RMSE.\"\"\"\n", "        if forecast.shape != observations.shape:\n", "            raise ValueError(\"Forecast and observations must have same shape\")\n", "        \n", "        diff = forecast - observations\n", "        mse = np.mean(diff ** 2)\n", "        return np.sqrt(mse)\n", "    \n", "    @property\n", "    def units(self) -> str:\n", "        return \"same as input\"\n", "\n", "class MAE(Metric):\n", "    \"\"\"Mean Absolute Error metric.\"\"\"\n", "    \n", "    def __init__(self):\n", "        super().__init__(\"MAE\")\n", "    \n", "    def compute(self, forecast: np.ndarray, observations: np.ndarray) -> float:\n", "        \"\"\"Compute MAE.\"\"\"\n", "        if forecast.shape != observations.shape:\n", "            raise ValueError(\"Forecast and observations must have same shape\")\n", "        \n", "        diff = np.abs(forecast - observations)\n", "        return np.mean(diff)\n", "    \n", "    @property\n", "    def units(self) -> str:\n", "        return \"same as input\"\n", "\n", "# WeatherBench2 Derived Variable Pattern\n", "@dataclasses.dataclass\n", "class DerivedVariable:\n", "    \"\"\"Base class for derived variables - WeatherBench2 pattern.\"\"\"\n", "    \n", "    @property\n", "    def base_variables(self) -> List[str]:\n", "        \"\"\"Variables needed to compute this derived variable.\"\"\"\n", "        return []\n", "    \n", "    def compute(self, dataset: Dict[str, np.ndarray]) -> np.ndarray:\n", "        \"\"\"Compute derived variable from dataset.\"\"\"\n", "        raise NotImplementedError\n", "\n", "@dataclasses.dataclass\n", "class WindSpeed(DerivedVariable):\n", "    \"\"\"Compute wind speed from u and v components.\"\"\"\n", "    u_component: str = 'u_component_of_wind'\n", "    v_component: str = 'v_component_of_wind'\n", "    \n", "    @property\n", "    def base_variables(self) -> List[str]:\n", "        return [self.u_component, self.v_component]\n", "    \n", "    def compute(self, dataset: Dict[str, np.ndarray]) -> np.ndarray:\n", "        \"\"\"Compute wind speed.\"\"\"\n", "        u = dataset[self.u_component]\n", "        v = dataset[self.v_component]\n", "        return np.sqrt(u**2 + v**2)\n", "\n", "# Test WeatherBench2 patterns\n", "print(f\"\\n🔧 Configuration Example:\")\n", "config = EvaluationConfig(\n", "    selection=Selection(\n", "        variables=['temperature', 'humidity'],\n", "        levels=[850, 500, 250]\n", "    ),\n", "    paths=Paths(\n", "        forecast='/data/forecasts/',\n", "        observations='/data/obs/',\n", "        output='/results/'\n", "    ),\n", "    metrics={'rmse': 'RMSE', 'mae': 'MAE'}\n", ")\n", "\n", "print(f\"   Variables: {config.selection.variables}\")\n", "print(f\"   Levels: {config.selection.levels}\")\n", "print(f\"   Metrics: {list(config.metrics.keys())}\")\n", "print(f\"   Valid config: {config.validate()}\")\n", "\n", "# Test metrics\n", "print(f\"\\n📊 Metrics Example:\")\n", "forecast_data = np.array([22.5, 23.1, 21.8, 24.2])\n", "observed_data = np.array([22.0, 23.5, 21.5, 24.0])\n", "\n", "rmse_metric = RMSE()\n", "mae_metric = MAE()\n", "\n", "rmse_value = rmse_metric.compute(forecast_data, observed_data)\n", "mae_value = mae_metric.compute(forecast_data, observed_data)\n", "\n", "print(f\"   RMSE: {rmse_value:.3f} {rmse_metric.units}\")\n", "print(f\"   MAE: {mae_value:.3f} {mae_metric.units}\")\n", "\n", "# Test derived variables\n", "print(f\"\\n🌪️ Derived Variables Example:\")\n", "wind_data = {\n", "    'u_component_of_wind': np.array([5.0, 3.0, -2.0, 8.0]),\n", "    'v_component_of_wind': np.array([3.0, 4.0, 6.0, -1.0])\n", "}\n", "\n", "wind_speed_calc = WindSpeed()\n", "wind_speeds = wind_speed_calc.compute(wind_data)\n", "\n", "print(f\"   Required variables: {wind_speed_calc.base_variables}\")\n", "print(f\"   U components: {wind_data['u_component_of_wind']}\")\n", "print(f\"   V components: {wind_data['v_component_of_wind']}\")\n", "print(f\"   Wind speeds: {wind_speeds.round(2)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---\n", "\n", "# 🎓 **Python Fundamentals Mastery Summary**\n", "\n", "## ✅ **Core Concepts Mastered:**\n", "\n", "### **1. Data Structures**\n", "- **Lists**: Dynamic arrays, list comprehensions, slicing\n", "- **Dictionaries**: Key-value mappings, dict comprehensions, safe access\n", "- **Sets**: Unique collections, set operations, filtering\n", "- **Tuples**: Immutable sequences, unpacking, named tuples\n", "\n", "### **2. Advanced Collections**\n", "- **defaultdict**: Automatic default values for grouping\n", "- **Counter**: Frequency analysis and counting\n", "- **deque**: Efficient sliding windows and queues\n", "- **Custom containers**: Building specialized data structures\n", "\n", "### **3. Object-Oriented Programming**\n", "- **Classes**: Basic OOP, methods, properties\n", "- **Dataclasses**: Modern Python data containers\n", "- **Inheritance**: Abstract base classes, polymorphism\n", "- **Protocols**: Duck typing and interfaces\n", "\n", "### **4. Functional Programming**\n", "- **Generators**: Memory-efficient data processing\n", "- **Decorators**: Function enhancement and metaprogramming\n", "- **Iterators**: Custom iteration patterns\n", "- **Higher-order functions**: functools patterns\n", "\n", "### **5. <PERSON><PERSON><PERSON>ling**\n", "- **Exception handling**: try/except patterns\n", "- **Custom exceptions**: Domain-specific error types\n", "- **Context managers**: Resource management\n", "- **Error recovery**: Robust data processing\n", "\n", "### **6. WeatherBench2 Pat<PERSON>s**\n", "- **Configuration classes**: Dataclass-based config\n", "- **Abstract metrics**: Plugin-style architecture\n", "- **Derived variables**: Computation pipelines\n", "- **Type hints**: Modern Python typing\n", "\n", "## 🚀 **Interview-Ready Skills:**\n", "\n", "You can now confidently:\n", "- **Write efficient Python code** using appropriate data structures\n", "- **Design object-oriented systems** with proper inheritance\n", "- **<PERSON><PERSON> errors gracefully** with comprehensive exception handling\n", "- **Use functional programming** patterns for elegant solutions\n", "- **Optimize performance** with generators and efficient algorithms\n", "- **Follow modern Python practices** with type hints and dataclasses\n", "\n", "## 💡 **Key Principles:**\n", "\n", "1. **Use list comprehensions** instead of loops when possible\n", "2. **Choose the right data structure** for the problem\n", "3. **Handle errors explicitly** - don't let them crash your program\n", "4. **Use generators** for memory-efficient data processing\n", "5. **Follow the DRY principle** - Don't Repeat Yourself\n", "6. **Write readable code** - code is read more than written\n", "\n", "---\n", "\n", "**🐍 You're now equipped with comprehensive Python knowledge for weather data analysis and coding interviews!**\n", "\n", "These patterns are directly applicable to:\n", "- **Data processing pipelines**\n", "- **Scientific computing**\n", "- **Weather forecasting systems**\n", "- **Machine learning workflows**\n", "- **Production Python applications**\n"]}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}