"""
DEBUGGING & TESTING CHALLENGES - Gridmatic Interview Prep
=========================================================

Interactive challenges to test your debugging and testing skills
Inspired by WeatherBench2 project patterns
Focus: Finding bugs, writing tests, handling edge cases
"""

import numpy as np
import xarray as xr
import pandas as pd
from typing import Dict, List, Tuple, Optional, Union
from datetime import datetime, timedelta
import warnings
import traceback


# ============================================================================
# CHALLENGE 1: DEBUG THE WEATHER DATA PROCESSOR
# ============================================================================

class BuggyWeatherProcessor:
    """This class has several bugs - find and fix them!"""
    
    def __init__(self, station_id: str):
        self.station_id = station_id
        self.readings = []
        self.cache = {}
    
    def add_reading(self, temperature: float, timestamp: str):
        """Add a temperature reading - BUG ALERT!"""
        # Bug 1: No validation
        reading = {
            'temp': temperature,
            'time': timestamp,
            'station': self.station_id
        }
        self.readings.append(reading)
    
    def get_daily_average(self, date: str) -> float:
        """Calculate daily average temperature - BUG ALERT!"""
        daily_temps = []
        
        for reading in self.readings:
            # Bug 2: String comparison instead of date comparison
            if reading['time'].startswith(date):
                daily_temps.append(reading['temp'])
        
        # Bug 3: Division by zero possible
        return sum(daily_temps) / len(daily_temps)
    
    def find_extreme_days(self, threshold: float) -> List[str]:
        """Find days with extreme temperatures - BUG ALERT!"""
        extreme_days = []
        daily_averages = {}
        
        # Bug 4: Inefficient nested loop
        for reading in self.readings:
            date = reading['time'][:10]  # Get date part
            if date not in daily_averages:
                daily_averages[date] = []
            daily_averages[date].append(reading['temp'])
        
        for date, temps in daily_averages.items():
            avg_temp = sum(temps) / len(temps)
            # Bug 5: Logic error - should be absolute value
            if avg_temp > threshold:
                extreme_days.append(date)
        
        return extreme_days
    
    def interpolate_missing_data(self, readings: List[Dict]) -> List[Dict]:
        """Interpolate missing temperature data - BUG ALERT!"""
        result = []
        
        for i, reading in enumerate(readings):
            if reading['temp'] is None:
                # Bug 6: Index out of bounds possible
                prev_temp = readings[i-1]['temp']
                next_temp = readings[i+1]['temp']
                
                # Bug 7: No handling of None values in neighbors
                interpolated = (prev_temp + next_temp) / 2
                reading['temp'] = interpolated
            
            result.append(reading)
        
        return result


def debug_challenge_1():
    """
    DEBUGGING CHALLENGE 1: Weather Data Processor
    
    The BuggyWeatherProcessor class above has 7 bugs.
    Your task:
    1. Identify each bug
    2. Explain why it's a problem
    3. Write the corrected version
    4. Write test cases to catch these bugs
    
    Bugs to find:
    - Validation issues
    - Logic errors  
    - Edge case handling
    - Performance problems
    - Index bounds errors
    """
    
    print("DEBUGGING CHALLENGE 1: Find 7 bugs in BuggyWeatherProcessor")
    print("=" * 60)
    
    # Test the buggy processor
    processor = BuggyWeatherProcessor("STATION_A")
    
    # This should reveal some bugs
    try:
        processor.add_reading(25.5, "2023-01-01T12:00:00")
        processor.add_reading(None, "2023-01-02T12:00:00")  # Bug potential
        
        avg = processor.get_daily_average("2023-01-01")  # Bug potential
        print(f"Daily average: {avg}")
        
        extremes = processor.find_extreme_days(30.0)
        print(f"Extreme days: {extremes}")
        
    except Exception as e:
        print(f"Error caught: {e}")
        print("This error reveals a bug!")
    
    return "Your turn: Find and fix all 7 bugs!"


# SOLUTION FOR CHALLENGE 1
class FixedWeatherProcessor:
    """Corrected version with all bugs fixed"""
    
    def __init__(self, station_id: str):
        if not station_id or not isinstance(station_id, str):
            raise ValueError("Station ID must be a non-empty string")
        
        self.station_id = station_id
        self.readings = []
        self.cache = {}
    
    def add_reading(self, temperature: float, timestamp: str):
        """Add a temperature reading with validation"""
        # Fix Bug 1: Add validation
        if temperature is not None and not isinstance(temperature, (int, float)):
            raise ValueError("Temperature must be a number")
        
        if not isinstance(timestamp, str):
            raise ValueError("Timestamp must be a string")
        
        # Validate timestamp format
        try:
            datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
        except ValueError:
            raise ValueError("Invalid timestamp format")
        
        reading = {
            'temp': temperature,
            'time': timestamp,
            'station': self.station_id
        }
        self.readings.append(reading)
    
    def get_daily_average(self, date: str) -> Optional[float]:
        """Calculate daily average temperature with proper date handling"""
        daily_temps = []
        
        for reading in self.readings:
            # Fix Bug 2: Proper date comparison
            reading_date = reading['time'][:10]
            if reading_date == date and reading['temp'] is not None:
                daily_temps.append(reading['temp'])
        
        # Fix Bug 3: Handle empty list
        if not daily_temps:
            return None
        
        return sum(daily_temps) / len(daily_temps)
    
    def find_extreme_days(self, threshold: float) -> List[str]:
        """Find days with extreme temperatures - fixed"""
        daily_averages = {}
        
        # Fix Bug 4: Single pass to calculate averages
        for reading in self.readings:
            if reading['temp'] is not None:
                date = reading['time'][:10]
                if date not in daily_averages:
                    daily_averages[date] = []
                daily_averages[date].append(reading['temp'])
        
        extreme_days = []
        for date, temps in daily_averages.items():
            if temps:  # Ensure not empty
                avg_temp = sum(temps) / len(temps)
                # Fix Bug 5: Use absolute value for extreme detection
                if abs(avg_temp - 20) > threshold:  # Assuming 20°C as baseline
                    extreme_days.append(date)
        
        return extreme_days
    
    def interpolate_missing_data(self, readings: List[Dict]) -> List[Dict]:
        """Interpolate missing temperature data safely"""
        if not readings:
            return []
        
        result = []
        
        for i, reading in enumerate(readings):
            if reading.get('temp') is None:
                # Fix Bug 6: Bounds checking
                prev_temp = None
                next_temp = None
                
                # Find previous valid temperature
                for j in range(i-1, -1, -1):
                    if readings[j].get('temp') is not None:
                        prev_temp = readings[j]['temp']
                        break
                
                # Find next valid temperature
                for j in range(i+1, len(readings)):
                    if readings[j].get('temp') is not None:
                        next_temp = readings[j]['temp']
                        break
                
                # Fix Bug 7: Handle None values properly
                if prev_temp is not None and next_temp is not None:
                    interpolated = (prev_temp + next_temp) / 2
                    reading = reading.copy()  # Don't modify original
                    reading['temp'] = interpolated
                elif prev_temp is not None:
                    reading = reading.copy()
                    reading['temp'] = prev_temp
                elif next_temp is not None:
                    reading = reading.copy()
                    reading['temp'] = next_temp
                # If both None, leave as None
            
            result.append(reading)
        
        return result


# ============================================================================
# CHALLENGE 2: DEBUG THE XARRAY WEATHER ANALYSIS
# ============================================================================

def buggy_weather_analysis(forecast_ds: xr.Dataset, obs_ds: xr.Dataset) -> Dict:
    """
    BUGGY FUNCTION: Calculate forecast skill metrics
    This function has several bugs - find them!
    """
    
    # Bug 1: No alignment of datasets
    metrics = {}
    
    for var in forecast_ds.data_vars:
        # Bug 2: Not checking if variable exists in observations
        fc = forecast_ds[var]
        obs = obs_ds[var]
        
        # Bug 3: Wrong dimension for calculation
        diff = fc - obs
        rmse = (diff ** 2).mean()  # Should be sqrt
        
        # Bug 4: Division by zero possible
        mae = abs(diff).sum() / diff.size
        
        # Bug 5: Correlation calculation wrong
        correlation = np.corrcoef(fc.values, obs.values)[0, 1]
        
        metrics[var] = {
            'rmse': rmse,
            'mae': mae,
            'correlation': correlation
        }
    
    return metrics


def debug_challenge_2():
    """
    DEBUGGING CHALLENGE 2: XArray Weather Analysis
    
    The buggy_weather_analysis function has 5 bugs.
    Find them and write the corrected version.
    """
    
    print("\nDEBUGGING CHALLENGE 2: XArray Analysis Function")
    print("=" * 60)
    
    # Create test data
    time = pd.date_range('2023-01-01', periods=10, freq='6H')
    lat = np.linspace(-90, 90, 19)
    lon = np.linspace(0, 360, 36)
    
    # Forecast data
    fc_data = 20 + 5 * np.random.randn(len(time), len(lat), len(lon))
    forecast_ds = xr.Dataset({
        'temperature': (['time', 'latitude', 'longitude'], fc_data)
    }, coords={'time': time, 'latitude': lat, 'longitude': lon})
    
    # Observation data (different coordinates to trigger bugs)
    obs_time = time[:-2]  # Missing last 2 time points
    obs_data = 18 + 6 * np.random.randn(len(obs_time), len(lat), len(lon))
    obs_ds = xr.Dataset({
        'temperature': (['time', 'latitude', 'longitude'], obs_data),
        'humidity': (['time', 'latitude', 'longitude'], 
                     50 + 20 * np.random.randn(len(obs_time), len(lat), len(lon)))
    }, coords={'time': obs_time, 'latitude': lat, 'longitude': lon})
    
    try:
        result = buggy_weather_analysis(forecast_ds, obs_ds)
        print("Result:", result)
    except Exception as e:
        print(f"Error: {e}")
        print("This error reveals bugs in the function!")
    
    return "Find and fix the 5 bugs in buggy_weather_analysis!"


# SOLUTION FOR CHALLENGE 2
def fixed_weather_analysis(forecast_ds: xr.Dataset, obs_ds: xr.Dataset) -> Dict:
    """Fixed version of weather analysis function"""
    
    # Fix Bug 1: Align datasets properly
    forecast_aligned, obs_aligned = xr.align(forecast_ds, obs_ds, join='inner')
    
    metrics = {}
    
    for var in forecast_aligned.data_vars:
        # Fix Bug 2: Check if variable exists in observations
        if var not in obs_aligned.data_vars:
            print(f"Warning: Variable {var} not found in observations")
            continue
        
        fc = forecast_aligned[var]
        obs = obs_aligned[var]
        
        # Calculate difference
        diff = fc - obs
        
        # Fix Bug 3: Proper RMSE calculation
        rmse = float(np.sqrt((diff ** 2).mean()))
        
        # Fix Bug 4: Proper MAE calculation
        mae = float(np.abs(diff).mean())
        
        # Fix Bug 5: Safe correlation calculation
        fc_flat = fc.values.flatten()
        obs_flat = obs.values.flatten()
        
        # Remove NaN values
        mask = ~(np.isnan(fc_flat) | np.isnan(obs_flat))
        
        if mask.sum() > 1:
            correlation = float(np.corrcoef(fc_flat[mask], obs_flat[mask])[0, 1])
        else:
            correlation = np.nan
        
        metrics[var] = {
            'rmse': rmse,
            'mae': mae,
            'correlation': correlation
        }
    
    return metrics


# ============================================================================
# CHALLENGE 3: WRITE COMPREHENSIVE TESTS
# ============================================================================

class TestWeatherProcessor:
    """Write comprehensive tests for the weather processor"""
    
    @staticmethod
    def test_basic_functionality():
        """Test basic add_reading and get_daily_average"""
        processor = FixedWeatherProcessor("TEST_STATION")
        
        # Test normal case
        processor.add_reading(25.0, "2023-01-01T12:00:00")
        processor.add_reading(27.0, "2023-01-01T18:00:00")
        
        avg = processor.get_daily_average("2023-01-01")
        expected = 26.0
        
        assert abs(avg - expected) < 0.001, f"Expected {expected}, got {avg}"
        print("✓ Basic functionality test passed")
    
    @staticmethod
    def test_edge_cases():
        """Test edge cases and error conditions"""
        processor = FixedWeatherProcessor("TEST_STATION")
        
        # Test empty day
        avg = processor.get_daily_average("2023-01-01")
        assert avg is None, "Expected None for empty day"
        
        # Test with None temperature
        processor.add_reading(None, "2023-01-01T12:00:00")
        avg = processor.get_daily_average("2023-01-01")
        assert avg is None, "Expected None when all temps are None"
        
        print("✓ Edge cases test passed")
    
    @staticmethod
    def test_validation():
        """Test input validation"""
        # Test invalid station ID
        try:
            processor = FixedWeatherProcessor("")
            assert False, "Should raise ValueError for empty station ID"
        except ValueError:
            pass
        
        processor = FixedWeatherProcessor("VALID_STATION")
        
        # Test invalid temperature type
        try:
            processor.add_reading("not_a_number", "2023-01-01T12:00:00")
            assert False, "Should raise ValueError for invalid temperature"
        except ValueError:
            pass
        
        # Test invalid timestamp
        try:
            processor.add_reading(25.0, "invalid_timestamp")
            assert False, "Should raise ValueError for invalid timestamp"
        except ValueError:
            pass
        
        print("✓ Validation test passed")
    
    @staticmethod
    def test_interpolation():
        """Test the interpolation function"""
        processor = FixedWeatherProcessor("TEST_STATION")
        
        # Test normal interpolation
        readings = [
            {'temp': 20.0, 'time': '2023-01-01T06:00:00'},
            {'temp': None, 'time': '2023-01-01T12:00:00'},
            {'temp': 30.0, 'time': '2023-01-01T18:00:00'}
        ]
        
        result = processor.interpolate_missing_data(readings)
        assert result[1]['temp'] == 25.0, f"Expected 25.0, got {result[1]['temp']}"
        
        # Test edge case: missing at start
        readings = [
            {'temp': None, 'time': '2023-01-01T06:00:00'},
            {'temp': 25.0, 'time': '2023-01-01T12:00:00'}
        ]
        
        result = processor.interpolate_missing_data(readings)
        assert result[0]['temp'] == 25.0, "Should use next valid value"
        
        print("✓ Interpolation test passed")
    
    @staticmethod
    def run_all_tests():
        """Run all tests"""
        print("RUNNING COMPREHENSIVE TESTS")
        print("=" * 40)
        
        TestWeatherProcessor.test_basic_functionality()
        TestWeatherProcessor.test_edge_cases()
        TestWeatherProcessor.test_validation()
        TestWeatherProcessor.test_interpolation()
        
        print("=" * 40)
        print("✅ ALL TESTS PASSED!")


# ============================================================================
# CHALLENGE 4: PERFORMANCE DEBUGGING
# ============================================================================

def slow_temperature_analysis(data: List[Dict]) -> Dict:
    """
    PERFORMANCE CHALLENGE: This function is very slow
    Find the performance bottlenecks and optimize it
    """
    
    # Performance Bug 1: Nested loops
    station_pairs = []
    for i, record1 in enumerate(data):
        for j, record2 in enumerate(data):
            if i != j and record1['station'] != record2['station']:
                station_pairs.append((record1['station'], record2['station']))
    
    # Performance Bug 2: Repeated calculations
    daily_averages = {}
    for date in [record['date'] for record in data]:  # Bug: duplicates
        daily_temps = []
        for record in data:
            if record['date'] == date:
                daily_temps.append(record['temp'])
        if daily_temps:
            daily_averages[date] = sum(daily_temps) / len(daily_temps)
    
    # Performance Bug 3: Inefficient data structure usage
    extreme_days = []
    for date, avg in daily_averages.items():
        is_extreme = False
        for other_date, other_avg in daily_averages.items():
            if abs(avg - other_avg) > 10:
                is_extreme = True
                break
        if is_extreme:
            extreme_days.append(date)
    
    return {
        'station_pairs': len(station_pairs),
        'daily_averages': len(daily_averages),
        'extreme_days': len(extreme_days)
    }


def optimized_temperature_analysis(data: List[Dict]) -> Dict:
    """Optimized version - identify the optimizations made"""
    
    # Optimization 1: Use sets for unique station pairs
    stations = set(record['station'] for record in data)
    station_pairs_count = len(stations) * (len(stations) - 1)
    
    # Optimization 2: Single pass for daily averages
    daily_data = {}
    for record in data:
        date = record['date']
        if date not in daily_data:
            daily_data[date] = []
        daily_data[date].append(record['temp'])
    
    daily_averages = {
        date: sum(temps) / len(temps)
        for date, temps in daily_data.items()
        if temps
    }
    
    # Optimization 3: More efficient extreme detection
    avg_values = list(daily_averages.values())
    if len(avg_values) < 2:
        extreme_days = []
    else:
        min_avg = min(avg_values)
        max_avg = max(avg_values)
        
        extreme_days = [
            date for date, avg in daily_averages.items()
            if abs(avg - min_avg) > 10 or abs(avg - max_avg) > 10
        ]
    
    return {
        'station_pairs': station_pairs_count,
        'daily_averages': len(daily_averages),
        'extreme_days': len(extreme_days)
    }


# ============================================================================
# CHALLENGE 5: INTEGRATION TESTING
# ============================================================================

def integration_test_challenge():
    """
    INTEGRATION TESTING CHALLENGE
    
    Test the complete workflow:
    1. Load weather data
    2. Process and clean it
    3. Calculate metrics
    4. Handle errors gracefully
    
    Write tests that verify the entire pipeline works correctly.
    """
    
    print("\nINTEGRATION TESTING CHALLENGE")
    print("=" * 60)
    
    # Simulate real-world messy data
    raw_data = [
        "STATION_A,25.5,2023-01-01T12:00:00",
        "STATION_B,invalid_temp,2023-01-01T12:00:00",  # Bad data
        "STATION_A,27.0,2023-01-01T18:00:00",
        "",  # Empty line
        "STATION_C,22.1,2023-01-02T06:00:00",
        "INVALID_FORMAT",  # Malformed line
        "STATION_A,28.5,2023-01-02T12:00:00",
    ]
    
    # Your task: Write integration tests that verify:
    # 1. Data parsing handles errors gracefully
    # 2. Weather processor works with real data
    # 3. Metrics calculation is accurate
    # 4. End-to-end workflow produces expected results
    
    def parse_raw_data(lines: List[str]) -> List[Dict]:
        """Parse raw weather data lines"""
        parsed = []
        errors = 0
        
        for line in lines:
            if not line.strip():
                continue
            
            try:
                parts = line.strip().split(',')
                if len(parts) != 3:
                    errors += 1
                    continue
                
                station, temp_str, timestamp = parts
                temperature = float(temp_str)
                
                parsed.append({
                    'station': station,
                    'temp': temperature,
                    'timestamp': timestamp
                })
                
            except ValueError:
                errors += 1
                continue
        
        return parsed, errors
    
    # Test the integration
    parsed_data, parse_errors = parse_raw_data(raw_data)
    print(f"Parsed {len(parsed_data)} records with {parse_errors} errors")
    
    # Process with weather processor
    processors = {}
    for record in parsed_data:
        station = record['station']
        if station not in processors:
            processors[station] = FixedWeatherProcessor(station)
        
        processors[station].add_reading(record['temp'], record['timestamp'])
    
    # Calculate daily averages
    results = {}
    for station, processor in processors.items():
        avg_jan1 = processor.get_daily_average("2023-01-01")
        avg_jan2 = processor.get_daily_average("2023-01-02")
        
        results[station] = {
            '2023-01-01': avg_jan1,
            '2023-01-02': avg_jan2
        }
    
    print("Integration test results:")
    for station, averages in results.items():
        print(f"  {station}: {averages}")
    
    # Verify expected results
    expected_station_a_jan1 = (25.5 + 27.0) / 2  # 26.25
    actual = results['STATION_A']['2023-01-01']
    
    assert abs(actual - expected_station_a_jan1) < 0.001, \
        f"Integration test failed: expected {expected_station_a_jan1}, got {actual}"
    
    print("✅ Integration test passed!")
    
    return results


# ============================================================================
# MAIN INTERACTIVE TESTING FRAMEWORK
# ============================================================================

def run_all_debugging_challenges():
    """Run all debugging and testing challenges"""
    
    print("DEBUGGING & TESTING CHALLENGES")
    print("=" * 60)
    print("These challenges test your ability to:")
    print("- Find and fix bugs in existing code")
    print("- Write comprehensive tests")
    print("- Handle edge cases and errors")
    print("- Optimize performance")
    print("- Debug integration issues")
    print()
    
    # Challenge 1: Debug the weather processor
    print("Challenge 1:")
    debug_challenge_1()
    
    # Challenge 2: Debug xarray analysis
    print("\nChallenge 2:")
    debug_challenge_2()
    
    # Challenge 3: Run comprehensive tests
    print("\nChallenge 3:")
    TestWeatherProcessor.run_all_tests()
    
    # Challenge 4: Performance testing
    print("\nChallenge 4: Performance Analysis")
    print("=" * 40)
    
    # Create test data
    test_data = [
        {'station': f'STATION_{i%3}', 'date': f'2023-01-{(i%10)+1:02d}', 'temp': 20 + i}
        for i in range(100)
    ]
    
    import time
    
    # Test slow version
    start = time.time()
    slow_result = slow_temperature_analysis(test_data)
    slow_time = time.time() - start
    
    # Test optimized version
    start = time.time()
    fast_result = optimized_temperature_analysis(test_data)
    fast_time = time.time() - start
    
    print(f"Slow version: {slow_time:.4f}s")
    print(f"Fast version: {fast_time:.4f}s")
    print(f"Speedup: {slow_time/fast_time:.1f}x faster")
    
    # Challenge 5: Integration testing
    print("\nChallenge 5:")
    integration_test_challenge()
    
    print("\n" + "=" * 60)
    print("🎯 DEBUGGING CHALLENGES COMPLETED!")
    print("Key skills demonstrated:")
    print("✓ Bug identification and fixing")
    print("✓ Comprehensive test writing")
    print("✓ Edge case handling")
    print("✓ Performance optimization")
    print("✓ Integration testing")


# ============================================================================
# INTERACTIVE DEBUGGING QUIZ
# ============================================================================

def debugging_quiz():
    """Interactive quiz to test debugging skills"""
    
    questions = [
        {
            'question': "What's wrong with this code?\n\ndef calculate_average(numbers):\n    return sum(numbers) / len(numbers)",
            'options': [
                'A) Nothing wrong',
                'B) Division by zero if empty list',
                'C) Should use numpy.mean()',
                'D) Missing type hints'
            ],
            'correct': 'B',
            'explanation': 'If numbers is an empty list, len(numbers) is 0, causing division by zero error.'
        },
        {
            'question': "What bug is in this xarray operation?\n\nresult = ds.sel(time='2023-01-01').mean('lat')",
            'options': [
                'A) Should use .mean(dim=\'lat\')',
                'B) Date string format wrong',
                'C) Missing method=\'nearest\' in sel()',
                'D) Nothing wrong'
            ],
            'correct': 'C',
            'explanation': 'If exact date doesn\'t exist, sel() will fail. Use method=\'nearest\' for robust selection.'
        },
        {
            'question': "What's the performance issue?\n\nfor i in range(len(data)):\n    for j in range(len(data)):\n        if data[i] == data[j] and i != j:\n            duplicates.append(i)",
            'options': [
                'A) O(n²) complexity',
                'B) Should use set for duplicates',
                'C) Could use Counter from collections',
                'D) All of the above'
            ],
            'correct': 'D',
            'explanation': 'Nested loops create O(n²) complexity. Using set or Counter would be much more efficient.'
        }
    ]
    
    print("\nDEBUGGING SKILLS QUIZ")
    print("=" * 40)
    
    score = 0
    for i, q in enumerate(questions, 1):
        print(f"\nQuestion {i}:")
        print(q['question'])
        print()
        for option in q['options']:
            print(option)
        
        user_answer = input("\nYour answer (A/B/C/D): ").upper().strip()
        
        if user_answer == q['correct']:
            print("✅ Correct!")
            score += 1
        else:
            print(f"❌ Wrong. Correct answer: {q['correct']}")
        
        print(f"Explanation: {q['explanation']}")
    
    print(f"\nFinal Score: {score}/{len(questions)}")
    
    if score == len(questions):
        print("🎉 Perfect! You have excellent debugging skills!")
    elif score >= len(questions) * 0.7:
        print("👍 Good job! Your debugging skills are solid.")
    else:
        print("📚 Keep practicing! Review the explanations and try again.")


if __name__ == "__main__":
    print("DEBUGGING & TESTING CHALLENGES FOR GRIDMATIC PREP")
    print("=" * 60)
    print("\nThis module tests your debugging and testing abilities.")
    print("Essential skills for any coding interview!\n")
    
    # Run all challenges
    run_all_debugging_challenges()
    
    # Interactive quiz
    print("\nWant to test your debugging knowledge?")
    response = input("Run debugging quiz? (y/n): ").lower().strip()
    if response == 'y':
        debugging_quiz()
    
    print("\n🎯 Key Takeaways:")
    print("- Always validate inputs")
    print("- Handle edge cases (empty data, None values)")
    print("- Write tests for both normal and error conditions")
    print("- Consider performance implications")
    print("- Test the complete workflow, not just individual functions")