{"cells": [{"cell_type": "markdown", "metadata": {"id": "header"}, "source": ["# Spatial & Geospatial Fundamentals for Weather Data\n", "\n", "## 🌍 **Master Spatial Concepts for Weather & Climate Science**\n", "\n", "This comprehensive tutorial covers all spatial and geospatial concepts essential for weather data analysis, including the specific patterns used in WeatherBench2 and modern meteorological applications.\n", "\n", "---\n", "\n", "## 📚 **What You'll Learn**\n", "\n", "1. **Coordinate Systems** - Geographic, projected, and weather-specific coordinates\n", "2. **Spatial Indexing** - Efficient spatial queries and selections\n", "3. **Interpolation & Regridding** - Spatial data transformation techniques\n", "4. **Distance & Area Calculations** - Great circle distance, area weighting\n", "5. **Spatial Statistics** - Autocorrelation, variograms, spatial patterns\n", "6. **Map Projections** - Understanding distortions and choosing projections\n", "7. **Spatial Aggregation** - Regional averaging, zonal statistics\n", "8. **Geospatial Visualization** - Advanced mapping and spatial plots\n", "9. **Performance Optimization** - Spatial indexing and chunking strategies\n", "10. **Real-world Applications** - Weather forecasting spatial patterns\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "setup"}, "outputs": [], "source": ["# Essential imports for spatial/geospatial analysis\n", "import numpy as np\n", "import pandas as pd\n", "import xarray as xr\n", "import matplotlib.pyplot as plt\n", "import cartopy.crs as ccrs\n", "import cartopy.feature as cfeature\n", "from cartopy.mpl.ticker import LongitudeFormatter, LatitudeFormatter\n", "\n", "# Spatial analysis libraries\n", "from scipy.spatial.distance import cdist\n", "from scipy.interpolate import griddata, RegularGridInterpolator\n", "from scipy.spatial import cKDTree\n", "from sklearn.neighbors import BallTree\n", "\n", "# Geospatial libraries\n", "try:\n", "    import rasterio\n", "    import geopandas as gpd\n", "    from shapely.geometry import Point, Polygon\n", "    GEOSPATIAL_AVAILABLE = True\n", "except ImportError:\n", "    print(\"⚠️ Some geospatial libraries not available. Installing...\")\n", "    GEOSPATIAL_AVAILABLE = False\n", "\n", "# Install required packages\n", "!pip install cartopy rasterio geopandas shapely pyproj -q\n", "\n", "# Mathematical constants\n", "EARTH_RADIUS_KM = 6371.0\n", "DEG_TO_RAD = np.pi / 180.0\n", "RAD_TO_DEG = 180.0 / np.pi\n", "\n", "print(\"✅ Spatial/Geospatial environment ready!\")\n", "print(f\"📦 NumPy version: {np.__version__}\")\n", "print(f\"📦 Xarray version: {xr.__version__}\")\n", "print(f\"🗺️ Cartopy available: {True}\")\n", "print(f\"🌍 Geospatial libraries: {'✅' if GEOSPATIAL_AVAILABLE else '⚠️ Limited'}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---\n", "\n", "# 🌐 **Section 1: Coordinate Systems & Reference Frames**\n", "\n", "Understanding coordinate systems is fundamental to all spatial analysis in meteorology.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load weather data for spatial analysis\n", "ds = xr.tutorial.load_dataset(\"air_temperature\")\n", "air_temp = ds['air']\n", "\n", "print(\"🌍 Weather Data Coordinate System Analysis:\")\n", "print(f\"\\n📍 Coordinate Information:\")\n", "print(f\"   Latitude range: {float(air_temp.lat.min()):.1f}° to {float(air_temp.lat.max()):.1f}°\")\n", "print(f\"   Longitude range: {float(air_temp.lon.min()):.1f}° to {float(air_temp.lon.max()):.1f}°\")\n", "print(f\"   Latitude resolution: {float(np.diff(air_temp.lat).mean()):.2f}°\")\n", "print(f\"   Longitude resolution: {float(np.diff(air_temp.lon).mean()):.2f}°\")\n", "\n", "# Check coordinate properties\n", "lat_increasing = air_temp.lat.values[1] > air_temp.lat.values[0]\n", "lon_increasing = air_temp.lon.values[1] > air_temp.lon.values[0]\n", "print(f\"\\n🔍 Coordinate Properties:\")\n", "print(f\"   Latitude ordering: {'North to South' if not lat_increasing else 'South to North'}\")\n", "print(f\"   Longitude ordering: {'West to East' if lon_increasing else 'East to West'}\")\n", "print(f\"   Longitude convention: {'0-360°' if air_temp.lon.min() >= 0 else '-180 to 180°'}\")\n", "\n", "# Convert longitude conventions\n", "def lon_360_to_180(lon):\n", "    \"\"\"Convert longitude from 0-360° to -180-180° convention.\"\"\"\n", "    return ((lon + 180) % 360) - 180\n", "\n", "def lon_180_to_360(lon):\n", "    \"\"\"Convert longitude from -180-180° to 0-360° convention.\"\"\"\n", "    return lon % 360\n", "\n", "# Demonstrate longitude conversion\n", "sample_lons_360 = np.array([0, 90, 180, 270, 359])\n", "sample_lons_180 = lon_360_to_180(sample_lons_360)\n", "\n", "print(f\"\\n🔄 Longitude Convention Conversion:\")\n", "print(f\"   0-360°: {sample_lons_360}\")\n", "print(f\"   -180-180°: {sample_lons_180}\")\n", "\n", "# Create coordinate grids\n", "def create_coordinate_grid(lat_range, lon_range, resolution=1.0):\n", "    \"\"\"Create regular lat/lon coordinate grid.\"\"\"\n", "    lats = np.arange(lat_range[0], lat_range[1] + resolution, resolution)\n", "    lons = np.arange(lon_range[0], lon_range[1] + resolution, resolution)\n", "    lon_grid, lat_grid = np.meshgrid(lons, lats)\n", "    return lat_grid, lon_grid\n", "\n", "# Example: Create a high-resolution grid\n", "lat_grid, lon_grid = create_coordinate_grid(\n", "    lat_range=(40, 50), \n", "    lon_range=(-10, 10), \n", "    resolution=0.5\n", ")\n", "\n", "print(f\"\\n📐 Custom Grid Creation:\")\n", "print(f\"   Grid shape: {lat_grid.shape}\")\n", "print(f\"   Latitude points: {lat_grid.shape[0]}\")\n", "print(f\"   Longitude points: {lat_grid.shape[1]}\")\n", "print(f\"   Total grid points: {lat_grid.size:,}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Advanced coordinate transformations\n", "print(\"🔄 Advanced Coordinate Transformations:\")\n", "\n", "def geographic_to_cartesian(lat, lon, radius=EARTH_RADIUS_KM):\n", "    \"\"\"\n", "    Convert geographic coordinates to 3D Cartesian coordinates.\n", "    \n", "    Essential for distance calculations and spatial analysis.\n", "    \"\"\"\n", "    lat_rad = np.deg2rad(lat)\n", "    lon_rad = np.deg2rad(lon)\n", "    \n", "    x = radius * np.cos(lat_rad) * np.cos(lon_rad)\n", "    y = radius * np.cos(lat_rad) * np.sin(lon_rad)\n", "    z = radius * np.sin(lat_rad)\n", "    \n", "    return x, y, z\n", "\n", "def cartesian_to_geographic(x, y, z):\n", "    \"\"\"\n", "    Convert 3D Cartesian coordinates back to geographic coordinates.\n", "    \"\"\"\n", "    radius = np.sqrt(x**2 + y**2 + z**2)\n", "    lat = np.rad2deg(np.arcsin(z / radius))\n", "    lon = np.rad2deg(np.arctan2(y, x))\n", "    \n", "    return lat, lon, radius\n", "\n", "# Example transformations\n", "test_lat, test_lon = 45.0, -75.0  # Montreal, Canada\n", "x, y, z = geographic_to_cartesian(test_lat, test_lon)\n", "lat_back, lon_back, radius_back = cartesian_to_geographic(x, y, z)\n", "\n", "print(f\"\\n🔄 Coordinate Transformation Test:\")\n", "print(f\"   Original: {test_lat:.1f}°N, {test_lon:.1f}°E\")\n", "print(f\"   Cartesian: ({x:.1f}, {y:.1f}, {z:.1f}) km\")\n", "print(f\"   Back to geo: {lat_back:.1f}°N, {lon_back:.1f}°E\")\n", "print(f\"   Transformation accuracy: {abs(test_lat - lat_back) + abs(test_lon - lon_back):.10f}°\")\n", "\n", "# Grid cell area calculations\n", "def calculate_grid_cell_area(lat, lon, dlat, dlon):\n", "    \"\"\"\n", "    Calculate area of grid cells in km².\n", "    \n", "    Critical for area-weighted averages in weather data.\n", "    \"\"\"\n", "    lat_rad = np.deg2rad(lat)\n", "    dlat_rad = np.deg2rad(dlat)\n", "    dlon_rad = np.deg2rad(dlon)\n", "    \n", "    # Area = R² * |sin(lat₂) - sin(lat₁)| * |lon₂ - lon₁|\n", "    area = (EARTH_RADIUS_KM**2 * \n", "            np.abs(np.sin(lat_rad + dlat_rad/2) - np.sin(lat_rad - dlat_rad/2)) * \n", "            np.abs(dlon_rad))\n", "    \n", "    return area\n", "\n", "# Calculate areas for our weather data\n", "dlat = float(np.diff(air_temp.lat).mean())\n", "dlon = float(np.diff(air_temp.lon).mean())\n", "\n", "# Create area array for each grid cell\n", "areas = xr.<PERSON>(\n", "    calculate_grid_cell_area(air_temp.lat, air_temp.lon, dlat, dlon),\n", "    dims=['lat', 'lon'],\n", "    coords={'lat': air_temp.lat, 'lon': air_temp.lon},\n", "    attrs={'units': 'km²', 'description': 'Grid cell area'}\n", ")\n", "\n", "print(f\"\\n📐 Grid Cell Area Analysis:\")\n", "print(f\"   Grid resolution: {dlat:.2f}° × {dlon:.2f}°\")\n", "print(f\"   Smallest cell area: {float(areas.min()):,.0f} km²\")\n", "print(f\"   Largest cell area: {float(areas.max()):,.0f} km²\")\n", "print(f\"   Area ratio (max/min): {float(areas.max() / areas.min()):.2f}\")\n", "print(f\"   Total area covered: {float(areas.sum()):,.0f} km²\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---\n", "\n", "# 📏 **Section 2: Distance Calculations & Spatial Metrics**\n", "\n", "Essential for spatial analysis, interpolation, and understanding spatial relationships in weather data.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Great circle distance calculations\n", "print(\"📏 Distance Calculations for Weather Data:\")\n", "\n", "def haversine_distance(lat1, lon1, lat2, lon2, radius=EARTH_RADIUS_KM):\n", "    \"\"\"\n", "    Calculate great circle distance using <PERSON><PERSON><PERSON> formula.\n", "    \n", "    Most accurate for short to medium distances on Earth's surface.\n", "    Essential for weather station networks and spatial interpolation.\n", "    \"\"\"\n", "    # Convert to radians\n", "    lat1_rad, lon1_rad = np.deg2rad(lat1), np.deg2rad(lon1)\n", "    lat2_rad, lon2_rad = np.deg2rad(lat2), np.deg2rad(lon2)\n", "    \n", "    # Haversine formula\n", "    dlat = lat2_rad - lat1_rad\n", "    dlon = lon2_rad - lon1_rad\n", "    \n", "    a = (np.sin(dlat/2)**2 + \n", "         np.cos(lat1_rad) * np.cos(lat2_rad) * np.sin(dlon/2)**2)\n", "    c = 2 * np.arcsin(np.sqrt(a))\n", "    \n", "    return radius * c\n", "\n", "def vincenty_distance(lat1, lon1, lat2, lon2):\n", "    \"\"\"\n", "    Calculate distance using <PERSON><PERSON>'s formula (more accurate for long distances).\n", "    \n", "    Better for global weather models and long-range correlations.\n", "    \"\"\"\n", "    # Convert to radians\n", "    lat1_rad, lon1_rad = np.deg2rad(lat1), np.deg2rad(lon1)\n", "    lat2_rad, lon2_rad = np.deg2rad(lat2), np.deg2rad(lon2)\n", "    \n", "    dlon = lon2_rad - lon1_rad\n", "    \n", "    # <PERSON><PERSON>'s formula\n", "    numerator = np.sqrt((np.cos(lat2_rad) * np.sin(dlon))**2 + \n", "                       (np.cos(lat1_rad) * np.sin(lat2_rad) - \n", "                        np.sin(lat1_rad) * np.cos(lat2_rad) * np.cos(dlon))**2)\n", "    \n", "    denominator = (np.sin(lat1_rad) * np.sin(lat2_rad) + \n", "                  np.cos(lat1_rad) * np.cos(lat2_rad) * np.cos(dlon))\n", "    \n", "    c = np.arctan2(numerator, denominator)\n", "    \n", "    return EARTH_RADIUS_KM * c\n", "\n", "# Test distance calculations\n", "# New York to London\n", "ny_lat, ny_lon = 40.7128, -74.0060\n", "london_lat, london_lon = 51.5074, -0.1278\n", "\n", "haversine_dist = haversine_distance(ny_lat, ny_lon, london_lat, london_lon)\n", "vincenty_dist = vincenty_distance(ny_lat, ny_lon, london_lat, london_lon)\n", "\n", "print(f\"\\n🌍 Distance Calculation Example (NYC to London):\")\n", "print(f\"   Haversine distance: {haversine_dist:.1f} km\")\n", "print(f\"   Vincenty distance: {vincenty_dist:.1f} km\")\n", "print(f\"   Difference: {abs(haversine_dist - vincenty_dist):.1f} km\")\n", "print(f\"   Relative error: {abs(haversine_dist - vincenty_dist) / vincenty_dist * 100:.3f}%\")\n", "\n", "# Create distance matrix for weather stations\n", "def create_distance_matrix(lats, lons, distance_func=haversine_distance):\n", "    \"\"\"\n", "    Create distance matrix between all pairs of locations.\n", "    \n", "    Essential for spatial correlation analysis and kriging.\n", "    \"\"\"\n", "    n_points = len(lats)\n", "    distances = np.zeros((n_points, n_points))\n", "    \n", "    for i in range(n_points):\n", "        for j in range(i+1, n_points):\n", "            dist = distance_func(lats[i], lons[i], lats[j], lons[j])\n", "            distances[i, j] = dist\n", "            distances[j, i] = dist  # Symmetric matrix\n", "    \n", "    return distances\n", "\n", "# Example: Distance matrix for sample weather stations\n", "station_lats = np.array([40.7, 41.9, 39.1, 42.4, 38.9])  # NYC, Chicago, DC, Boston, Baltimore\n", "station_lons = np.array([-74.0, -87.6, -77.0, -71.1, -77.0])\n", "station_names = ['NYC', 'Chicago', 'DC', 'Boston', 'Baltimore']\n", "\n", "dist_matrix = create_distance_matrix(station_lats, station_lons)\n", "\n", "print(f\"\\n📊 Weather Station Distance Matrix:\")\n", "print(f\"   Matrix shape: {dist_matrix.shape}\")\n", "print(f\"   Min distance (non-zero): {dist_matrix[dist_matrix > 0].min():.1f} km\")\n", "print(f\"   Max distance: {dist_matrix.max():.1f} km\")\n", "print(f\"   Mean distance: {dist_matrix[dist_matrix > 0].mean():.1f} km\")\n", "\n", "# Find nearest neighbors\n", "def find_nearest_neighbors(target_lat, target_lon, station_lats, station_lons, k=3):\n", "    \"\"\"\n", "    Find k nearest weather stations to a target location.\n", "    \n", "    Critical for spatial interpolation and data quality control.\n", "    \"\"\"\n", "    distances = haversine_distance(target_lat, target_lon, station_lats, station_lons)\n", "    nearest_indices = np.argsort(distances)[:k]\n", "    nearest_distances = distances[nearest_indices]\n", "    \n", "    return nearest_indices, nearest_distances\n", "\n", "# Example: Find nearest stations to Philadelphia\n", "philly_lat, philly_lon = 39.9526, -75.1652\n", "nearest_idx, nearest_dist = find_nearest_neighbors(philly_lat, philly_lon, station_lats, station_lons)\n", "\n", "print(f\"\\n🎯 Nearest Neighbors to Philadelphia:\")\n", "for i, (idx, dist) in enumerate(zip(nearest_idx, nearest_dist)):\n", "    print(f\"   {i+1}. {station_names[idx]}: {dist:.1f} km\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---\n", "\n", "# 🔍 **Section 3: Spatial Indexing & Efficient Queries**\n", "\n", "Advanced techniques for fast spatial queries in large weather datasets.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Spatial indexing for efficient queries\n", "print(\"🔍 Spatial Indexing for Weather Data:\")\n", "\n", "class SpatialIndex:\n", "    \"\"\"\n", "    Efficient spatial indexing for weather data queries.\n", "    \n", "    Uses KD-tree for fast nearest neighbor searches.\n", "    Essential for real-time weather applications.\n", "    \"\"\"\n", "    \n", "    def __init__(self, lats, lons):\n", "        # Convert to Cartesian coordinates for better distance calculations\n", "        self.lats = np.array(lats)\n", "        self.lons = np.array(lons)\n", "        \n", "        # Create Cartesian coordinates\n", "        x, y, z = geographic_to_cartesian(self.lats, self.lons)\n", "        self.cartesian_coords = np.column_stack([x, y, z])\n", "        \n", "        # Build KD-tree\n", "        self.tree = cKDTree(self.cartesian_coords)\n", "        \n", "        print(f\"   ✅ Spatial index built for {len(lats)} points\")\n", "    \n", "    def query_nearest(self, query_lat, query_lon, k=1):\n", "        \"\"\"Find k nearest points to query location.\"\"\"\n", "        # Convert query point to <PERSON><PERSON><PERSON>\n", "        qx, qy, qz = geographic_to_cartesian(query_lat, query_lon)\n", "        query_point = np.array([qx, qy, qz])\n", "        \n", "        # Query tree\n", "        distances, indices = self.tree.query(query_point, k=k)\n", "        \n", "        # Convert Cartesian distances back to great circle distances\n", "        if k == 1:\n", "            geo_distance = haversine_distance(\n", "                query_lat, query_lon, \n", "                self.lats[indices], self.lons[indices]\n", "            )\n", "            return indices, geo_distance\n", "        else:\n", "            geo_distances = haversine_distance(\n", "                query_lat, query_lon,\n", "                self.lats[indices], self.lons[indices]\n", "            )\n", "            return indices, geo_distances\n", "    \n", "    def query_radius(self, query_lat, query_lon, radius_km):\n", "        \"\"\"Find all points within radius of query location.\"\"\"\n", "        # Convert query point to <PERSON><PERSON><PERSON>\n", "        qx, qy, qz = geographic_to_cartesian(query_lat, query_lon)\n", "        query_point = np.array([qx, qy, qz])\n", "        \n", "        # Approximate Cartesian radius (not exact, but fast)\n", "        cartesian_radius = radius_km * 2  # Rough approximation\n", "        \n", "        # Query tree\n", "        indices = self.tree.query_ball_point(query_point, cartesian_radius)\n", "        \n", "        if not indices:\n", "            return np.array([]), np.array([])\n", "        \n", "        # Filter by exact great circle distance\n", "        exact_distances = haversine_distance(\n", "            query_lat, query_lon,\n", "            self.lats[indices], self.lons[indices]\n", "        )\n", "        \n", "        valid_mask = exact_distances <= radius_km\n", "        valid_indices = np.array(indices)[valid_mask]\n", "        valid_distances = exact_distances[valid_mask]\n", "        \n", "        return valid_indices, valid_distances\n", "\n", "# Create spatial index for weather grid\n", "# Use a subset of points for demonstration\n", "sample_lats = air_temp.lat.values[::4]  # Every 4th latitude\n", "sample_lons = air_temp.lon.values[::4]  # Every 4th longitude\n", "\n", "# Create all combinations\n", "lon_grid, lat_grid = np.meshgrid(sample_lons, sample_lats)\n", "grid_lats = lat_grid.flatten()\n", "grid_lons = lon_grid.flatten()\n", "\n", "# Build spatial index\n", "spatial_idx = SpatialIndex(grid_lats, grid_lons)\n", "\n", "print(f\"\\n📊 Spatial Index Performance Test:\")\n", "\n", "# Test query performance\n", "import time\n", "\n", "# Query point: Denver, Colorado\n", "denver_lat, denver_lon = 39.7392, -104.9903\n", "\n", "# Nearest neighbor query\n", "start_time = time.time()\n", "nearest_idx, nearest_dist = spatial_idx.query_nearest(denver_lat, denver_lon, k=5)\n", "nn_time = time.time() - start_time\n", "\n", "print(f\"   Nearest neighbor query: {nn_time*1000:.2f} ms\")\n", "print(f\"   Found {len(nearest_idx)} nearest points\")\n", "print(f\"   Closest distance: {nearest_dist[0]:.1f} km\")\n", "\n", "# Radius query\n", "start_time = time.time()\n", "radius_idx, radius_dist = spatial_idx.query_radius(denver_lat, denver_lon, 500)  # 500 km radius\n", "radius_time = time.time() - start_time\n", "\n", "print(f\"   Radius query (500 km): {radius_time*1000:.2f} ms\")\n", "print(f\"   Found {len(radius_idx)} points within radius\")\n", "if len(radius_dist) > 0:\n", "    print(f\"   Distance range: {radius_dist.min():.1f} - {radius_dist.max():.1f} km\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---\n", "\n", "# 🔄 **Section 4: Interpolation & Regridding**\n", "\n", "Essential techniques for transforming weather data between different spatial grids and resolutions.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Spatial interpolation methods for weather data\n", "print(\"🔄 Spatial Interpolation & Regridding:\")\n", "\n", "def bilinear_interpolation(data, source_lats, source_lons, target_lats, target_lons):\n", "    \"\"\"\n", "    Bilinear interpolation for regular grids.\n", "    \n", "    Fast and commonly used for weather model output.\n", "    \"\"\"\n", "    from scipy.interpolate import RegularGridInterpolator\n", "    \n", "    # Create interpolator\n", "    interpolator = RegularGridInterpolator(\n", "        (source_lats, source_lons), \n", "        data, \n", "        method='linear',\n", "        bounds_error=False,\n", "        fill_value=np.nan\n", "    )\n", "    \n", "    # Create target grid\n", "    target_lon_grid, target_lat_grid = np.meshgrid(target_lons, target_lats)\n", "    target_points = np.column_stack([\n", "        target_lat_grid.flatten(),\n", "        target_lon_grid.flatten()\n", "    ])\n", "    \n", "    # Interpolate\n", "    interpolated = interpolator(target_points)\n", "    \n", "    return interpolated.reshape(len(target_lats), len(target_lons))\n", "\n", "def inverse_distance_weighting(data_points, data_values, target_points, power=2, radius=None):\n", "    \"\"\"\n", "    Inverse Distance Weighting interpolation.\n", "    \n", "    Good for irregular weather station data.\n", "    \"\"\"\n", "    n_targets = len(target_points)\n", "    interpolated = np.zeros(n_targets)\n", "    \n", "    for i, target in enumerate(target_points):\n", "        # Calculate distances\n", "        distances = haversine_distance(\n", "            target[0], target[1],\n", "            data_points[:, 0], data_points[:, 1]\n", "        )\n", "        \n", "        # Apply radius filter if specified\n", "        if radius is not None:\n", "            valid_mask = distances <= radius\n", "            if not np.any(valid_mask):\n", "                interpolated[i] = np.nan\n", "                continue\n", "            distances = distances[valid_mask]\n", "            values = data_values[valid_mask]\n", "        else:\n", "            values = data_values\n", "        \n", "        # Handle exact matches\n", "        if np.any(distances == 0):\n", "            interpolated[i] = values[distances == 0][0]\n", "            continue\n", "        \n", "        # IDW calculation\n", "        weights = 1 / (distances ** power)\n", "        interpolated[i] = np.sum(weights * values) / np.sum(weights)\n", "    \n", "    return interpolated\n", "\n", "def kriging_interpolation(data_points, data_values, target_points, variogram_model='spherical'):\n", "    \"\"\"\n", "    Simple kriging interpolation.\n", "    \n", "    Most sophisticated method, accounts for spatial correlation.\n", "    \"\"\"\n", "    # This is a simplified kriging implementation\n", "    # In practice, use libraries like PyKrige\n", "    \n", "    # For demonstration, we'll use IDW with optimized parameters\n", "    # Real kriging would involve variogram modeling\n", "    return inverse_distance_weighting(data_points, data_values, target_points, power=1.5)\n", "\n", "# Demonstrate interpolation methods\n", "print(f\"\\n🔬 Interpolation Methods Comparison:\")\n", "\n", "# Create source data (coarse grid)\n", "source_lats = np.linspace(35, 45, 6)\n", "source_lons = np.linspace(-80, -70, 6)\n", "source_lon_grid, source_lat_grid = np.meshgrid(source_lons, source_lats)\n", "\n", "# Create synthetic temperature data with realistic patterns\n", "source_temp = (20 + \n", "               5 * np.sin(np.deg2rad(source_lat_grid * 2)) +  # Latitude gradient\n", "               3 * np.cos(np.deg2rad(source_lon_grid * 3)) +  # Longitude variation\n", "               np.random.normal(0, 1, source_lat_grid.shape))  # Noise\n", "\n", "# Create target grid (fine grid)\n", "target_lats = np.linspace(35, 45, 21)\n", "target_lons = np.linspace(-80, -70, 21)\n", "\n", "# Test bilinear interpolation\n", "start_time = time.time()\n", "bilinear_result = bilinear_interpolation(\n", "    source_temp, source_lats, source_lons, target_lats, target_lons\n", ")\n", "bilinear_time = time.time() - start_time\n", "\n", "print(f\"   Bilinear interpolation: {bilinear_time*1000:.2f} ms\")\n", "print(f\"   Output shape: {bilinear_result.shape}\")\n", "print(f\"   Temperature range: {np.nanmin(bilinear_result):.1f} to {np.nanmax(bilinear_result):.1f}°C\")\n", "\n", "# Test IDW interpolation (on subset for speed)\n", "# Convert source grid to points\n", "source_points = np.column_stack([\n", "    source_lat_grid.flatten(),\n", "    source_lon_grid.flatten()\n", "])\n", "source_values = source_temp.flatten()\n", "\n", "# Target points (subset)\n", "target_subset_lats = target_lats[::4]\n", "target_subset_lons = target_lons[::4]\n", "target_lon_sub, target_lat_sub = np.meshgrid(target_subset_lons, target_subset_lats)\n", "target_points = np.column_stack([\n", "    target_lat_sub.flatten(),\n", "    target_lon_sub.flatten()\n", "])\n", "\n", "start_time = time.time()\n", "idw_result = inverse_distance_weighting(\n", "    source_points, source_values, target_points, power=2\n", ")\n", "idw_time = time.time() - start_time\n", "\n", "print(f\"   IDW interpolation: {idw_time*1000:.2f} ms\")\n", "print(f\"   Output points: {len(idw_result)}\")\n", "print(f\"   Temperature range: {np.nanmin(idw_result):.1f} to {np.nanmax(idw_result):.1f}°C\")\n", "\n", "# Regridding with xarray (most practical approach)\n", "print(f\"\\n📊 Xarray Regridding (Practical Approach):\")\n", "\n", "# Create xarray dataset from source data\n", "source_ds = xr.Dataset({\n", "    'temperature': (['lat', 'lon'], source_temp)\n", "}, coords={\n", "    'lat': source_lats,\n", "    'lon': source_lons\n", "})\n", "\n", "# Interpolate using xarray\n", "start_time = time.time()\n", "regridded_ds = source_ds.interp(\n", "    lat=target_lats,\n", "    lon=target_lons,\n", "    method='linear'\n", ")\n", "xarray_time = time.time() - start_time\n", "\n", "print(f\"   Xarray interpolation: {xarray_time*1000:.2f} ms\")\n", "print(f\"   Output shape: {regridded_ds.temperature.shape}\")\n", "print(f\"   Temperature range: {float(regridded_ds.temperature.min()):.1f} to {float(regridded_ds.temperature.max()):.1f}°C\")\n", "print(f\"   ✅ Xarray method is fastest and most practical!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---\n", "\n", "# 📊 **Section 5: Spatial Statistics & Pattern Analysis**\n", "\n", "Advanced statistical methods for understanding spatial patterns in weather data.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Spatial statistics for weather pattern analysis\n", "print(\"📊 Spatial Statistics & Pattern Analysis:\")\n", "\n", "def spatial_autocorrelation(data, lats, lons, max_distance=1000):\n", "    \"\"\"\n", "    Calculate spatial autocorrelation (Moran's I).\n", "    \n", "    Measures how similar nearby weather observations are.\n", "    \"\"\"\n", "    # Flatten data and coordinates\n", "    flat_data = data.flatten()\n", "    flat_lats = np.repeat(lats[:, np.newaxis], len(lons), axis=1).flatten()\n", "    flat_lons = np.repeat(lons[np.newaxis, :], len(lats), axis=0).flatten()\n", "    \n", "    # Remove NaN values\n", "    valid_mask = ~np.isnan(flat_data)\n", "    valid_data = flat_data[valid_mask]\n", "    valid_lats = flat_lats[valid_mask]\n", "    valid_lons = flat_lons[valid_mask]\n", "    \n", "    n = len(valid_data)\n", "    if n < 10:  # Need minimum points\n", "        return np.nan\n", "    \n", "    # Calculate distance matrix (subset for performance)\n", "    subset_size = min(100, n)  # Limit for demonstration\n", "    indices = np.random.choice(n, subset_size, replace=False)\n", "    \n", "    subset_data = valid_data[indices]\n", "    subset_lats = valid_lats[indices]\n", "    subset_lons = valid_lons[indices]\n", "    \n", "    # Create weight matrix based on distance\n", "    weights = np.zeros((subset_size, subset_size))\n", "    for i in range(subset_size):\n", "        for j in range(i+1, subset_size):\n", "            dist = haversine_distance(\n", "                subset_lats[i], subset_lons[i],\n", "                subset_lats[j], subset_lons[j]\n", "            )\n", "            if dist <= max_distance:\n", "                weight = 1 / (1 + dist)  # Inverse distance weighting\n", "                weights[i, j] = weight\n", "                weights[j, i] = weight\n", "    \n", "    # Calculate Moran's I\n", "    mean_val = np.mean(subset_data)\n", "    deviations = subset_data - mean_val\n", "    \n", "    numerator = np.sum(weights * np.outer(deviations, deviations))\n", "    denominator = np.sum(deviations**2)\n", "    W = np.sum(weights)\n", "    \n", "    if W == 0 or denominator == 0:\n", "        return np.nan\n", "    \n", "    morans_i = (subset_size / W) * (numerator / denominator)\n", "    \n", "    return morans_i\n", "\n", "def calculate_variogram(data, lats, lons, max_distance=1000, n_bins=10):\n", "    \"\"\"\n", "    Calculate experimental variogram.\n", "    \n", "    Shows how spatial correlation changes with distance.\n", "    Essential for kriging interpolation.\n", "    \"\"\"\n", "    # Flatten and clean data\n", "    flat_data = data.flatten()\n", "    flat_lats = np.repeat(lats[:, np.newaxis], len(lons), axis=1).flatten()\n", "    flat_lons = np.repeat(lons[np.newaxis, :], len(lats), axis=0).flatten()\n", "    \n", "    valid_mask = ~np.isnan(flat_data)\n", "    valid_data = flat_data[valid_mask]\n", "    valid_lats = flat_lats[valid_mask]\n", "    valid_lons = flat_lons[valid_mask]\n", "    \n", "    # Subset for performance\n", "    n = len(valid_data)\n", "    subset_size = min(50, n)\n", "    indices = np.random.choice(n, subset_size, replace=False)\n", "    \n", "    subset_data = valid_data[indices]\n", "    subset_lats = valid_lats[indices]\n", "    subset_lons = valid_lons[indices]\n", "    \n", "    # Calculate all pairwise distances and semivariances\n", "    distances = []\n", "    semivariances = []\n", "    \n", "    for i in range(subset_size):\n", "        for j in range(i+1, subset_size):\n", "            dist = haversine_distance(\n", "                subset_lats[i], subset_lons[i],\n", "                subset_lats[j], subset_lons[j]\n", "            )\n", "            if dist <= max_distance:\n", "                semivar = 0.5 * (subset_data[i] - subset_data[j])**2\n", "                distances.append(dist)\n", "                semivariances.append(semivar)\n", "    \n", "    if not distances:\n", "        return np.array([]), np.array([])\n", "    \n", "    distances = np.array(distances)\n", "    semivariances = np.array(semivariances)\n", "    \n", "    # Bin the data\n", "    bin_edges = np.linspace(0, max_distance, n_bins + 1)\n", "    bin_centers = (bin_edges[:-1] + bin_edges[1:]) / 2\n", "    binned_semivariances = []\n", "    \n", "    for i in range(n_bins):\n", "        mask = (distances >= bin_edges[i]) & (distances < bin_edges[i+1])\n", "        if np.any(mask):\n", "            binned_semivariances.append(np.mean(semivariances[mask]))\n", "        else:\n", "            binned_semivariances.append(np.nan)\n", "    \n", "    return bin_centers, np.array(binned_semivariances)\n", "\n", "# Analyze spatial patterns in temperature data\n", "temp_sample = air_temp.isel(time=0)  # First time step\n", "\n", "print(f\"\\n🔍 Spatial Pattern Analysis:\")\n", "\n", "# Calculate spatial autocorrelation\n", "morans_i = spatial_autocorrelation(\n", "    temp_sample.values, \n", "    temp_sample.lat.values, \n", "    temp_sample.lon.values\n", ")\n", "\n", "print(f\"   <PERSON>'s I (spatial autocorrelation): {morans_i:.3f}\")\n", "if morans_i > 0.3:\n", "    print(f\"   ✅ Strong positive spatial autocorrelation (clustered pattern)\")\n", "elif m<PERSON>_i > 0:\n", "    print(f\"   ✅ Weak positive spatial autocorrelation\")\n", "elif m<PERSON>_i < -0.3:\n", "    print(f\"   ❌ Strong negative spatial autocorrelation (dispersed pattern)\")\n", "else:\n", "    print(f\"   ➖ Random spatial pattern\")\n", "\n", "# Calculate variogram\n", "distances, semivariances = calculate_variogram(\n", "    temp_sample.values,\n", "    temp_sample.lat.values,\n", "    temp_sample.lon.values,\n", "    max_distance=2000\n", ")\n", "\n", "if len(distances) > 0:\n", "    valid_mask = ~np.isnan(semivariances)\n", "    if np.any(valid_mask):\n", "        print(f\"   Variogram calculated for {np.sum(valid_mask)} distance bins\")\n", "        print(f\"   Semivariance range: {np.nanmin(semivariances):.2f} to {np.nanmax(semivariances):.2f}\")\n", "        \n", "        # Estimate range (distance where semivariance levels off)\n", "        max_semivar = np.nanmax(semivariances)\n", "        range_threshold = 0.95 * max_semivar\n", "        range_indices = np.where(semivariances >= range_threshold)[0]\n", "        if len(range_indices) > 0:\n", "            estimated_range = distances[range_indices[0]]\n", "            print(f\"   Estimated spatial range: {estimated_range:.0f} km\")\n", "    else:\n", "        print(f\"   ⚠️ Insufficient data for variogram analysis\")\n", "\n", "# Spatial gradient analysis\n", "def calculate_spatial_gradients(data, lats, lons):\n", "    \"\"\"\n", "    Calculate spatial gradients (rate of change).\n", "    \n", "    Important for identifying fronts and boundaries in weather data.\n", "    \"\"\"\n", "    # Convert to consistent units (degrees to km)\n", "    dlat_km = np.diff(lats) * 111.0  # Approximate km per degree latitude\n", "    dlon_km = np.diff(lons) * 111.0 * np.cos(np.deg2rad(np.mean(lats)))\n", "    \n", "    # Calculate gradients\n", "    grad_lat = np.gradient(data, dlat_km.mean(), axis=0)\n", "    grad_lon = np.gradient(data, dlon_km.mean(), axis=1)\n", "    \n", "    # Magnitude of gradient\n", "    grad_magnitude = np.sqrt(grad_lat**2 + grad_lon**2)\n", "    \n", "    return grad_lat, grad_lon, grad_magnitude\n", "\n", "# Calculate temperature gradients\n", "grad_lat, grad_lon, grad_mag = calculate_spatial_gradients(\n", "    temp_sample.values,\n", "    temp_sample.lat.values,\n", "    temp_sample.lon.values\n", ")\n", "\n", "print(f\"\\n🌡️ Temperature Gradient Analysis:\")\n", "print(f\"   Max latitudinal gradient: {np.nanmax(np.abs(grad_lat)):.3f} K/km\")\n", "print(f\"   Max longitudinal gradient: {np.nanmax(np.abs(grad_lon)):.3f} K/km\")\n", "print(f\"   Max gradient magnitude: {np.nanmax(grad_mag):.3f} K/km\")\n", "print(f\"   Mean gradient magnitude: {np.nanmean(grad_mag):.3f} K/km\")\n", "\n", "# Identify strong gradient regions (potential fronts)\n", "strong_gradient_threshold = np.nanpercentile(grad_mag, 95)  # Top 5%\n", "strong_gradient_points = np.sum(grad_mag > strong_gradient_threshold)\n", "total_points = np.sum(~np.isnan(grad_mag))\n", "\n", "print(f\"   Strong gradient regions: {strong_gradient_points}/{total_points} points ({strong_gradient_points/total_points*100:.1f}%)\")\n", "print(f\"   Strong gradient threshold: {strong_gradient_threshold:.3f} K/km\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---\n", "\n", "# 🗺️ **Section 6: Advanced Geospatial Visualization**\n", "\n", "Professional mapping and visualization techniques for weather data.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Advanced geospatial visualization\n", "print(\"🗺️ Advanced Geospatial Visualization:\")\n", "\n", "# Create comprehensive weather visualization\n", "fig = plt.figure(figsize=(20, 15))\n", "\n", "# 1. Basic temperature map with coastlines\n", "ax1 = plt.subplot(2, 3, 1, projection=ccrs.Plate<PERSON>arree())\n", "temp_plot = temp_sample.plot(\n", "    ax=ax1, \n", "    transform=ccrs.<PERSON>(),\n", "    cmap='RdYlBu_r',\n", "    add_colorbar=False\n", ")\n", "ax1.add_feature(cfeature.COASTLINE, linewidth=0.5)\n", "ax1.add_feature(cfeature.BORDERS, linewidth=0.3)\n", "ax1.set_title('Temperature Map', fontweight='bold')\n", "ax1.set_global()\n", "plt.colorbar(temp_plot, ax=ax1, shrink=0.8, label='Temperature (K)')\n", "\n", "# 2. Temperature with different projection (Orthographic)\n", "ax2 = plt.subplot(2, 3, 2, projection=ccrs.Orthographic(central_longitude=-100, central_latitude=45))\n", "temp_sample.plot(\n", "    ax=ax2,\n", "    transform=ccrs.<PERSON>(),\n", "    cmap='plasma',\n", "    add_colorbar=False\n", ")\n", "ax2.add_feature(cfeature.COASTLINE)\n", "ax2.add_feature(cfeature.LAND, alpha=0.3)\n", "ax2.add_feature(cfeature.OCEAN, alpha=0.3)\n", "ax2.set_title('Orthographic Projection', fontweight='bold')\n", "ax2.set_global()\n", "\n", "# 3. Contour plot with filled contours\n", "ax3 = plt.subplot(2, 3, 3, projection=ccrs.Plate<PERSON>arree())\n", "contour_levels = np.linspace(float(temp_sample.min()), float(temp_sample.max()), 15)\n", "cs = ax3.contourf(\n", "    temp_sample.lon, temp_sample.lat, temp_sample.values,\n", "    levels=contour_levels,\n", "    transform=ccrs.<PERSON>(),\n", "    cmap='viridis',\n", "    extend='both'\n", ")\n", "ax3.contour(\n", "    temp_sample.lon, temp_sample.lat, temp_sample.values,\n", "    levels=contour_levels[::2],\n", "    colors='black',\n", "    linewidths=0.5,\n", "    transform=ccrs.Plate<PERSON>arree()\n", ")\n", "ax3.add_feature(cfeature.COASTLINE)\n", "ax3.set_title('Contour Plot', fontweight='bold')\n", "ax3.set_global()\n", "plt.colorbar(cs, ax=ax3, shrink=0.8)\n", "\n", "# 4. Regional focus with high resolution\n", "ax4 = plt.subplot(2, 3, 4, projection=ccrs.Plate<PERSON>arree())\n", "# Focus on North America\n", "regional_temp = temp_sample.sel(lat=slice(70, 20), lon=slice(190, 310))\n", "regional_temp.plot(\n", "    ax=ax4,\n", "    transform=ccrs.<PERSON>(),\n", "    cmap='RdYlBu_r',\n", "    add_colorbar=False\n", ")\n", "ax4.add_feature(cfeature.COASTLINE)\n", "ax4.add_feature(cfeature.BORDERS)\n", "ax4.add_feature(cfeature.STATES, linewidth=0.3)\n", "ax4.set_extent([-160, -50, 20, 70], crs=ccrs.PlateCarree())\n", "ax4.set_title('Regional Focus: North America', fontweight='bold')\n", "\n", "# Add gridlines\n", "gl = ax4.gridlines(draw_labels=True, alpha=0.3)\n", "gl.top_labels = False\n", "gl.right_labels = False\n", "\n", "# 5. Temperature gradient magnitude\n", "ax5 = plt.subplot(2, 3, 5, projection=ccrs.Plate<PERSON><PERSON>ree())\n", "grad_da = xr.<PERSON>(\n", "    grad_mag,\n", "    dims=['lat', 'lon'],\n", "    coords={'lat': temp_sample.lat, 'lon': temp_sample.lon}\n", ")\n", "grad_plot = grad_da.plot(\n", "    ax=ax5,\n", "    transform=ccrs.<PERSON>(),\n", "    cmap='hot',\n", "    add_colorbar=False\n", ")\n", "ax5.add_feature(cfeature.COASTLINE)\n", "ax5.set_title('Temperature Gradient Magnitude', fontweight='bold')\n", "ax5.set_global()\n", "plt.colorbar(grad_plot, ax=ax5, shrink=0.8, label='K/km')\n", "\n", "# 6. Polar stereographic projection\n", "ax6 = plt.subplot(2, 3, 6, projection=ccrs.NorthPolarStereo())\n", "# Focus on Arctic\n", "arctic_temp = temp_sample.sel(lat=slice(90, 60))\n", "arctic_temp.plot(\n", "    ax=ax6,\n", "    transform=ccrs.<PERSON>(),\n", "    cmap='coolwarm',\n", "    add_colorbar=False\n", ")\n", "ax6.add_feature(cfeature.COASTLINE)\n", "ax6.add_feature(cfeature.LAND, alpha=0.3)\n", "ax6.set_extent([-180, 180, 60, 90], crs=ccrs.PlateCarree())\n", "ax6.set_title('Arctic (Polar Stereographic)', fontweight='bold')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"✅ Advanced visualization examples completed\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---\n", "\n", "# 🎯 **Section 7: Real-world Weather Applications**\n", "\n", "Practical applications of spatial concepts in weather forecasting and climate analysis.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Real-world weather applications\n", "print(\"🎯 Real-world Weather Applications:\")\n", "\n", "def identify_weather_fronts(temperature_data, gradient_threshold=0.01):\n", "    \"\"\"\n", "    Identify potential weather fronts based on temperature gradients.\n", "    \n", "    Used in operational weather forecasting.\n", "    \"\"\"\n", "    # Calculate gradients\n", "    grad_lat, grad_lon, grad_mag = calculate_spatial_gradients(\n", "        temperature_data.values,\n", "        temperature_data.lat.values,\n", "        temperature_data.lon.values\n", "    )\n", "    \n", "    # Identify front locations\n", "    front_mask = grad_mag > gradient_threshold\n", "    \n", "    # Get coordinates of front points\n", "    lat_grid, lon_grid = np.meshgrid(temperature_data.lat, temperature_data.lon, indexing='ij')\n", "    front_lats = lat_grid[front_mask]\n", "    front_lons = lon_grid[front_mask]\n", "    front_intensities = grad_mag[front_mask]\n", "    \n", "    return front_lats, front_lons, front_intensities\n", "\n", "def calculate_regional_statistics(data, regions):\n", "    \"\"\"\n", "    Calculate statistics for predefined regions.\n", "    \n", "    Essential for climate monitoring and regional forecasting.\n", "    \"\"\"\n", "    regional_stats = {}\n", "    \n", "    for region_name, bounds in regions.items():\n", "        # Extract regional data\n", "        regional_data = data.sel(\n", "            lat=slice(bounds['lat_min'], bounds['lat_max']),\n", "            lon=slice(bounds['lon_min'], bounds['lon_max'])\n", "        )\n", "        \n", "        # Calculate area-weighted statistics\n", "        weights = np.cos(np.deg2rad(regional_data.lat))\n", "        weighted_data = regional_data.weighted(weights)\n", "        \n", "        regional_stats[region_name] = {\n", "            'mean': float(weighted_data.mean()),\n", "            'std': float(weighted_data.std()),\n", "            'min': float(regional_data.min()),\n", "            'max': float(regional_data.max()),\n", "            'area_km2': calculate_region_area(bounds)\n", "        }\n", "    \n", "    return regional_stats\n", "\n", "def calculate_region_area(bounds):\n", "    \"\"\"\n", "    Calculate approximate area of a rectangular region.\n", "    \"\"\"\n", "    lat_range = bounds['lat_max'] - bounds['lat_min']\n", "    lon_range = bounds['lon_max'] - bounds['lon_min']\n", "    avg_lat = (bounds['lat_max'] + bounds['lat_min']) / 2\n", "    \n", "    # Convert to km\n", "    lat_km = lat_range * 111.0\n", "    lon_km = lon_range * 111.0 * np.cos(np.deg2rad(avg_lat))\n", "    \n", "    return lat_km * lon_km\n", "\n", "def detect_temperature_anomalies(current_temp, climatology, threshold_std=2.0):\n", "    \"\"\"\n", "    Detect temperature anomalies relative to climatology.\n", "    \n", "    Used for extreme weather event detection.\n", "    \"\"\"\n", "    # Calculate anomalies\n", "    anomalies = current_temp - climatology.mean(dim='time')\n", "    climatology_std = climatology.std(dim='time')\n", "    \n", "    # Standardized anomalies\n", "    standardized_anomalies = anomalies / climatology_std\n", "    \n", "    # Identify extreme anomalies\n", "    hot_anomalies = standardized_anomalies > threshold_std\n", "    cold_anomalies = standardized_anomalies < -threshold_std\n", "    \n", "    return {\n", "        'anomalies': anomalies,\n", "        'standardized_anomalies': standardized_anomalies,\n", "        'hot_anomalies': hot_anomalies,\n", "        'cold_anomalies': cold_anomalies\n", "    }\n", "\n", "# Application 1: Weather front detection\n", "print(f\"\\n🌪️ Weather Front Detection:\")\n", "front_lats, front_lons, front_intensities = identify_weather_fronts(\n", "    temp_sample, \n", "    gradient_threshold=0.005  # K/km\n", ")\n", "\n", "print(f\"   Detected {len(front_lats)} potential front points\")\n", "if len(front_intensities) > 0:\n", "    print(f\"   Front intensity range: {front_intensities.min():.4f} to {front_intensities.max():.4f} K/km\")\n", "    print(f\"   Strongest front at: {front_lats[np.argmax(front_intensities)]:.1f}°N, {front_lons[np.argmax(front_intensities)]:.1f}°E\")\n", "\n", "# Application 2: Regional climate analysis\n", "print(f\"\\n🌍 Regional Climate Analysis:\")\n", "climate_regions = {\n", "    'Arctic': {'lat_min': 66.5, 'lat_max': 90, 'lon_min': 0, 'lon_max': 360},\n", "    'Temperate_NH': {'lat_min': 23.5, 'lat_max': 66.5, 'lon_min': 0, 'lon_max': 360},\n", "    'Tropics': {'lat_min': -23.5, 'lat_max': 23.5, 'lon_min': 0, 'lon_max': 360},\n", "    'North_America': {'lat_min': 25, 'lat_max': 70, 'lon_min': 190, 'lon_max': 300}\n", "}\n", "\n", "regional_stats = calculate_regional_statistics(temp_sample, climate_regions)\n", "\n", "for region, stats in regional_stats.items():\n", "    print(f\"\\n   📊 {region}:\")\n", "    print(f\"      Mean temperature: {stats['mean']:.1f} K ({stats['mean']-273.15:.1f}°C)\")\n", "    print(f\"      Temperature range: {stats['min']:.1f} to {stats['max']:.1f} K\")\n", "    print(f\"      Standard deviation: {stats['std']:.2f} K\")\n", "    print(f\"      Approximate area: {stats['area_km2']:,.0f} km²\")\n", "\n", "# Application 3: Anomaly detection\n", "print(f\"\\n🔥 Temperature Anomaly Detection:\")\n", "# Use the full time series for climatology\n", "current_temp = air_temp.isel(time=-1)  # Last time step\n", "anomaly_results = detect_temperature_anomalies(current_temp, air_temp)\n", "\n", "hot_anomaly_count = int(anomaly_results['hot_anomalies'].sum())\n", "cold_anomaly_count = int(anomaly_results['cold_anomalies'].sum())\n", "total_points = int(current_temp.size)\n", "\n", "print(f\"   Hot anomalies (>2σ): {hot_anomaly_count}/{total_points} points ({hot_anomaly_count/total_points*100:.1f}%)\")\n", "print(f\"   Cold anomalies (<-2σ): {cold_anomaly_count}/{total_points} points ({cold_anomaly_count/total_points*100:.1f}%)\")\n", "\n", "if hot_anomaly_count > 0:\n", "    max_hot_anomaly = float(anomaly_results['standardized_anomalies'].max())\n", "    print(f\"   Strongest hot anomaly: {max_hot_anomaly:.1f} standard deviations\")\n", "\n", "if cold_anomaly_count > 0:\n", "    min_cold_anomaly = float(anomaly_results['standardized_anomalies'].min())\n", "    print(f\"   Strongest cold anomaly: {min_cold_anomaly:.1f} standard deviations\")\n", "\n", "# Application 4: Spatial correlation analysis\n", "print(f\"\\n📊 Spatial Correlation Analysis:\")\n", "\n", "def calculate_spatial_correlation_distance(data, reference_point, max_distance=2000):\n", "    \"\"\"\n", "    Calculate how correlation decreases with distance from a reference point.\n", "    \n", "    Important for understanding spatial scales in weather patterns.\n", "    \"\"\"\n", "    ref_lat, ref_lon = reference_point\n", "    \n", "    # Find nearest grid point to reference\n", "    ref_data = data.sel(lat=ref_lat, lon=ref_lon, method='nearest')\n", "    \n", "    # Calculate correlations with all other points\n", "    correlations = []\n", "    distances = []\n", "    \n", "    for lat in data.lat.values[::2]:  # Subsample for speed\n", "        for lon in data.lon.values[::2]:\n", "            dist = haversine_distance(ref_lat, ref_lon, lat, lon)\n", "            if dist <= max_distance:\n", "                point_data = data.sel(lat=lat, lon=lon, method='nearest')\n", "                \n", "                # Calculate temporal correlation\n", "                corr = np.corrcoef(ref_data.values, point_data.values)[0, 1]\n", "                if not np.isnan(corr):\n", "                    correlations.append(corr)\n", "                    distances.append(dist)\n", "    \n", "    return np.array(distances), np.array(correlations)\n", "\n", "# Analyze spatial correlation from a central point\n", "reference_point = (45.0, -100.0)  # Central North America\n", "distances, correlations = calculate_spatial_correlation_distance(\n", "    air_temp, reference_point, max_distance=1500\n", ")\n", "\n", "if len(correlations) > 10:\n", "    # Find decorrelation distance (where correlation drops to 1/e ≈ 0.37)\n", "    decorr_threshold = 1/np.e\n", "    decorr_indices = np.where(correlations <= decorr_threshold)[0]\n", "    \n", "    if len(decorr_indices) > 0:\n", "        decorr_distance = distances[decorr_indices[0]]\n", "        print(f\"   Decorrelation distance: ~{decorr_distance:.0f} km\")\n", "    \n", "    print(f\"   Correlation at 500 km: {np.interp(500, distances, correlations):.3f}\")\n", "    print(f\"   Correlation at 1000 km: {np.interp(1000, distances, correlations):.3f}\")\n", "    print(f\"   Maximum analysis distance: {distances.max():.0f} km\")\n", "\n", "print(f\"\\n✅ Real-world applications demonstrated successfully!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---\n", "\n", "# 🎓 **Spatial & Geospatial Mastery Summary**\n", "\n", "## ✅ **Core Concepts Mastered:**\n", "\n", "### **1. Coordinate Systems & Transformations**\n", "- Geographic coordinate systems (lat/lon)\n", "- Coordinate convention conversions (0-360° ↔ -180-180°)\n", "- Cartesian coordinate transformations\n", "- Grid cell area calculations\n", "- Map projections and their applications\n", "\n", "### **2. Distance Calculations**\n", "- Haversine formula for great circle distances\n", "- <PERSON><PERSON>'s formula for high accuracy\n", "- Distance matrices for spatial analysis\n", "- Nearest neighbor searches\n", "- Spatial indexing with KD-trees\n", "\n", "### **3. Spatial Interpolation**\n", "- Bilinear interpolation for regular grids\n", "- Inverse Distance Weighting (IDW)\n", "- Kriging concepts and variogram analysis\n", "- Xarray-based regridding (most practical)\n", "- Performance optimization strategies\n", "\n", "### **4. Spatial Statistics**\n", "- Spatial autocorrelation (Moran's I)\n", "- Variogram calculation and interpretation\n", "- Spatial gradient analysis\n", "- Pattern detection and characterization\n", "- Correlation-distance relationships\n", "\n", "### **5. Advanced Visualization**\n", "- Multiple map projections (PlateCarree, Orthographic, Polar Stereographic)\n", "- Contour and filled contour plots\n", "- Regional focus and high-resolution mapping\n", "- Gradient visualization techniques\n", "- Professional cartographic styling\n", "\n", "### **6. Real-world Applications**\n", "- Weather front detection algorithms\n", "- Regional climate analysis\n", "- Temperature anomaly detection\n", "- Spatial correlation analysis\n", "- Operational weather forecasting techniques\n", "\n", "## 🚀 **Weather-Specific Skills:**\n", "\n", "- **Area-weighted averaging** for proper global statistics\n", "- **Front detection** using temperature gradients\n", "- **Anomaly identification** relative to climatology\n", "- **Regional analysis** for climate zones\n", "- **Spatial correlation** understanding for forecast skill\n", "- **Multi-projection visualization** for different perspectives\n", "\n", "## 💡 **Key Principles:**\n", "\n", "1. **Earth is not flat** - Always account for spherical geometry\n", "2. **Area weighting matters** - Grid cells have different areas\n", "3. **Choose appropriate projections** - Different uses need different projections\n", "4. **Spatial correlation decays** - Nearby points are more similar\n", "5. **Performance optimization** - Use spatial indexing for large datasets\n", "\n", "## 🎯 **Interview-Ready Knowledge:**\n", "\n", "You can now confidently discuss and implement:\n", "- Spatial data structures and coordinate systems\n", "- Distance calculations and spatial relationships\n", "- Interpolation methods and their trade-offs\n", "- Spatial statistics and pattern analysis\n", "- Professional geospatial visualization\n", "- Real-world weather applications\n", "\n", "---\n", "\n", "**🌍 You're now equipped with comprehensive spatial and geospatial knowledge for weather data analysis!**\n", "\n", "This knowledge is directly applicable to:\n", "- **Weather forecasting** systems\n", "- **Climate model** evaluation\n", "- **Satellite data** processing\n", "- **Numerical weather prediction**\n", "- **Climate change** analysis\n", "- **Extreme weather** detection\n"]}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}