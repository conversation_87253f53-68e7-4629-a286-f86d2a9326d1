{"cells": [{"cell_type": "markdown", "metadata": {"id": "header"}, "source": ["# Xarray Fundamentals: Complete Learning Guide\n", "\n", "## 🎯 **Master Xarray for Scientific Computing**\n", "\n", "This comprehensive tutorial covers all fundamental concepts from the official xarray-tutorial repository. Perfect for quickly understanding and practicing xarray for scientific data analysis.\n", "\n", "---\n", "\n", "## 📚 **What You'll Learn**\n", "\n", "1. **Data Structures** - DataArray and Dataset fundamentals\n", "2. **Indexing & Selection** - Position-based and label-based indexing\n", "3. **Computation** - Arithmetic, reductions, and apply_ufunc\n", "4. **GroupBy Operations** - Split-apply-combine workflows\n", "5. **Plotting** - Visualization with matplotlib integration\n", "6. **Dask Integration** - Parallel and out-of-core computing\n", "7. **Real-world Examples** - Practical applications\n", "\n", "Based on the official [xarray-contrib/xarray-tutorial](https://github.com/xarray-contrib/xarray-tutorial) repository.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "setup"}, "outputs": [], "source": ["# Essential imports for xarray fundamentals\n", "import numpy as np\n", "import pandas as pd\n", "import xarray as xr\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "# Configure display settings for better learning\n", "xr.set_options(\n", "    display_expand_attrs=False,  # Don't show all attributes by default\n", "    display_expand_data=False,   # Don't show all data values by default\n", "    display_style=\"html\"         # Use interactive HTML display\n", ")\n", "\n", "# Configure matplotlib\n", "plt.style.use('default')\n", "%matplotlib inline\n", "\n", "print(\"✅ Xarray fundamentals environment ready!\")\n", "print(f\"📦 Xarray version: {xr.__version__}\")\n", "print(f\"📦 NumPy version: {np.__version__}\")\n", "print(f\"📦 Pandas version: {pd.__version__}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---\n", "\n", "# 📊 **Section 1: Data Structures - DataArray and Dataset**\n", "\n", "Understanding the core xarray data structures is fundamental to everything else.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load tutorial dataset - NCEP air temperature data\n", "ds = xr.tutorial.load_dataset(\"air_temperature\")\n", "\n", "print(\"🌍 Loaded NCEP Air Temperature Dataset\")\n", "print(\"📊 Dataset structure:\")\n", "display(ds)\n", "\n", "print(\"\\n🔍 Dataset components:\")\n", "print(f\"   📏 Dimensions: {dict(ds.dims)}\")\n", "print(f\"   📍 Coordinates: {list(ds.coords)}\")\n", "print(f\"   📈 Data variables: {list(ds.data_vars)}\")\n", "print(f\"   📝 Attributes: {len(ds.attrs)} metadata items\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Extract DataArray from Dataset\n", "air_temp = ds['air']  # or ds.air\n", "\n", "print(\"🌡️ Air Temperature DataArray:\")\n", "display(air_temp)\n", "\n", "print(\"\\n🔍 DataArray components:\")\n", "print(f\"   📏 Shape: {air_temp.shape}\")\n", "print(f\"   📏 Dimensions: {air_temp.dims}\")\n", "print(f\"   📍 Coordinates: {list(air_temp.coords)}\")\n", "print(f\"   🏷️ Name: {air_temp.name}\")\n", "print(f\"   📊 Data type: {air_temp.dtype}\")\n", "print(f\"   📝 Units: {air_temp.attrs.get('units', 'Not specified')}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Creating DataArrays manually - fundamental patterns\n", "print(\"🔨 Creating DataArrays manually:\")\n", "\n", "# 1D DataArray\n", "temp_1d = xr.<PERSON>(\n", "    data=[20, 22, 19, 25, 23],\n", "    dims=['time'],\n", "    coords={'time': pd.date_range('2023-01-01', periods=5, freq='D')},\n", "    attrs={'units': 'celsius', 'description': 'Daily temperature'}\n", ")\n", "\n", "print(\"\\n📈 1D Temperature DataArray:\")\n", "display(temp_1d)\n", "\n", "# 2D DataArray\n", "temp_2d = xr.<PERSON>(\n", "    data=np.random.randn(3, 4) * 5 + 20,\n", "    dims=['lat', 'lon'],\n", "    coords={\n", "        'lat': [40, 41, 42],\n", "        'lon': [-74, -73, -72, -71]\n", "    },\n", "    attrs={'units': 'celsius'}\n", ")\n", "\n", "print(\"\\n🗺️ 2D Temperature DataArray:\")\n", "display(temp_2d)\n", "\n", "# Creating Dataset from multiple DataArrays\n", "weather_ds = xr.Dataset({\n", "    'temperature': temp_2d,\n", "    'pressure': xr.<PERSON>(\n", "        data=np.random.randn(3, 4) * 10 + 1013,\n", "        dims=['lat', 'lon'],\n", "        coords=temp_2d.coords,\n", "        attrs={'units': 'hPa'}\n", "    )\n", "})\n", "\n", "print(\"\\n🌤️ Weather Dataset:\")\n", "display(weather_ds)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---\n", "\n", "# 🎯 **Section 2: Indexing and Selection**\n", "\n", "Master both position-based and label-based indexing - the key to effective data manipulation.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Position-based indexing with .isel()\n", "print(\"📍 Position-based Indexing Examples:\")\n", "\n", "# Select first time step\n", "first_time = air_temp.isel(time=0)\n", "print(f\"\\n🕐 First time step shape: {first_time.shape}\")\n", "print(f\"   Date: {first_time.time.values}\")\n", "\n", "# Select specific lat/lon point\n", "point_data = air_temp.isel(lat=10, lon=20)\n", "print(f\"\\n📍 Time series at one point shape: {point_data.shape}\")\n", "print(f\"   Location: {float(point_data.lat.values):.1f}°N, {float(point_data.lon.values):.1f}°E\")\n", "\n", "# Slice ranges\n", "subset = air_temp.isel(time=slice(0, 10), lat=slice(5, 15))\n", "print(f\"\\n✂️ Subset shape: {subset.shape}\")\n", "print(f\"   Time range: {subset.time.values[0]} to {subset.time.values[-1]}\")\n", "\n", "# Multiple indices\n", "scattered = air_temp.isel(time=[0, 10, 20], lat=[5, 10, 15])\n", "print(f\"\\n🎯 Scattered selection shape: {scattered.shape}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Label-based indexing with .sel()\n", "print(\"🏷️ Label-based Indexing Examples:\")\n", "\n", "# Select by coordinate values\n", "specific_location = air_temp.sel(lat=50.0, lon=250.0)\n", "print(f\"\\n📍 Specific location time series shape: {specific_location.shape}\")\n", "print(f\"   Location: {float(specific_location.lat.values)}°N, {float(specific_location.lon.values)}°E\")\n", "\n", "# Slice by coordinate ranges\n", "regional_data = air_temp.sel(\n", "    lat=slice(60, 40),  # Note: can go high to low\n", "    lon=slice(200, 250)\n", ")\n", "print(f\"\\n🗺️ Regional subset shape: {regional_data.shape}\")\n", "print(f\"   Lat range: {float(regional_data.lat.min()):.1f}° to {float(regional_data.lat.max()):.1f}°\")\n", "print(f\"   Lon range: {float(regional_data.lon.min()):.1f}° to {float(regional_data.lon.max()):.1f}°\")\n", "\n", "# Nearest neighbor selection\n", "nearest = air_temp.sel(lat=52.3, lon=248.7, method='nearest')\n", "print(f\"\\n🎯 Nearest neighbor selection:\")\n", "print(f\"   Requested: 52.3°N, 248.7°E\")\n", "print(f\"   Actual: {float(nearest.lat.values):.1f}°N, {float(nearest.lon.values):.1f}°E\")\n", "\n", "# Tolerance for nearest neighbor\n", "try:\n", "    strict_nearest = air_temp.sel(lat=52.3, lon=248.7, method='nearest', tolerance=1.0)\n", "    print(f\"   ✅ Within tolerance\")\n", "except KeyError:\n", "    print(f\"   ❌ Outside tolerance\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# DateTime indexing - crucial for time series analysis\n", "print(\"📅 DateTime Indexing Examples:\")\n", "\n", "# Select specific date\n", "specific_date = air_temp.sel(time='2013-01-01')\n", "print(f\"\\n📅 Specific date selection shape: {specific_date.shape}\")\n", "print(f\"   Selected times: {len(specific_date.time)} (all times for that day)\")\n", "\n", "# Select date range\n", "date_range = air_temp.sel(time=slice('2013-01-01', '2013-01-31'))\n", "print(f\"\\n📅 January 2013 shape: {date_range.shape}\")\n", "print(f\"   Time span: {date_range.time.values[0]} to {date_range.time.values[-1]}\")\n", "\n", "# Select by year\n", "year_2014 = air_temp.sel(time='2014')\n", "print(f\"\\n📅 Year 2014 shape: {year_2014.shape}\")\n", "\n", "# Select by month across all years\n", "july_data = air_temp.sel(time=air_temp.time.dt.month == 7)\n", "print(f\"\\n📅 All July data shape: {july_data.shape}\")\n", "print(f\"   Years covered: {july_data.time.dt.year.values.min()} to {july_data.time.dt.year.values.max()}\")\n", "\n", "# Select specific dates (non-contiguous)\n", "specific_dates = air_temp.sel(time=['2013-07-04', '2013-12-25', '2014-01-01'])\n", "print(f\"\\n📅 Specific holidays shape: {specific_dates.shape}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---\n", "\n", "# 🧮 **Section 3: Computation and Operations**\n", "\n", "Learn arithmetic, reductions, and advanced computation patterns with xarray.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Basic arithmetic operations\n", "print(\"➕ Basic Arithmetic Operations:\")\n", "\n", "# Convert <PERSON><PERSON> to <PERSON><PERSON><PERSON>\n", "air_celsius = air_temp - 273.15\n", "print(f\"\\n🌡️ Temperature conversion:\")\n", "print(f\"   Original range: {float(air_temp.min()):.1f} to {float(air_temp.max()):.1f} K\")\n", "print(f\"   Celsius range: {float(air_celsius.min()):.1f} to {float(air_celsius.max()):.1f} °C\")\n", "\n", "# Mathematical operations\n", "air_squared = air_temp ** 2\n", "air_log = np.log(air_temp)\n", "air_sqrt = np.sqrt(air_temp)\n", "\n", "print(f\"\\n🔢 Mathematical operations preserve structure:\")\n", "print(f\"   Original shape: {air_temp.shape}\")\n", "print(f\"   Squared shape: {air_squared.shape}\")\n", "print(f\"   Log shape: {air_log.shape}\")\n", "print(f\"   Sqrt shape: {air_sqrt.shape}\")\n", "\n", "# Operations between DataArrays\n", "temp_anomaly = air_temp - air_temp.mean(dim='time')\n", "print(f\"\\n📊 Temperature anomaly:\")\n", "print(f\"   Anomaly range: {float(temp_anomaly.min()):.2f} to {float(temp_anomaly.max()):.2f} K\")\n", "print(f\"   Mean anomaly: {float(temp_anomaly.mean()):.6f} K (should be ~0)\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Reduction operations - the heart of data analysis\n", "print(\"📉 Reduction Operations:\")\n", "\n", "# Temporal reductions\n", "time_mean = air_temp.mean(dim='time')\n", "time_std = air_temp.std(dim='time')\n", "time_max = air_temp.max(dim='time')\n", "time_min = air_temp.min(dim='time')\n", "\n", "print(f\"\\n⏰ Temporal statistics:\")\n", "print(f\"   Time mean shape: {time_mean.shape} (time dimension removed)\")\n", "print(f\"   Global mean temp: {float(time_mean.mean()):.2f} K\")\n", "print(f\"   Global temp std: {float(time_std.mean()):.2f} K\")\n", "print(f\"   Global temp range: {float(time_min.min()):.1f} to {float(time_max.max()):.1f} K\")\n", "\n", "# Spatial reductions\n", "spatial_mean = air_temp.mean(dim=['lat', 'lon'])\n", "print(f\"\\n🗺️ Spatial average time series shape: {spatial_mean.shape}\")\n", "print(f\"   Global mean temp over time: {float(spatial_mean.mean()):.2f} K\")\n", "\n", "# Multiple dimension reductions\n", "overall_stats = {\n", "    'mean': float(air_temp.mean()),\n", "    'std': float(air_temp.std()),\n", "    'min': float(air_temp.min()),\n", "    'max': float(air_temp.max()),\n", "    'median': float(air_temp.median())\n", "}\n", "\n", "print(f\"\\n📊 Overall dataset statistics:\")\n", "for stat, value in overall_stats.items():\n", "    print(f\"   {stat.capitalize()}: {value:.2f} K\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Advanced computation with apply_ufunc\n", "print(\"🔧 Advanced Computation with apply_ufunc:\")\n", "\n", "# Custom function that doesn't natively work with xarray\n", "def custom_transform(x):\n", "    \"\"\"Custom transformation function.\"\"\"\n", "    return np.where(x > x.mean(), x * 1.1, x * 0.9)\n", "\n", "# Apply custom function while preserving xarray structure\n", "transformed = xr.apply_ufunc(\n", "    custom_transform,\n", "    air_temp,\n", "    input_core_dims=[[]],  # No core dimensions\n", "    output_core_dims=[[]],  # No core dimensions\n", "    dask='allowed'  # Allow dask arrays\n", ")\n", "\n", "print(f\"\\n🔄 Custom transformation:\")\n", "print(f\"   Original shape: {air_temp.shape}\")\n", "print(f\"   Transformed shape: {transformed.shape}\")\n", "print(f\"   Coordinates preserved: {list(transformed.coords) == list(air_temp.coords)}\")\n", "\n", "# Using numpy functions that don't preserve xarray structure\n", "# Example: nan_to_num\n", "clean_data = xr.apply_ufunc(\n", "    np.nan_to_num,\n", "    air_temp,\n", "    kwargs={'nan': 0.0, 'posinf': 1000.0, 'neginf': -1000.0}\n", ")\n", "\n", "print(f\"\\n🧹 Cleaned data:\")\n", "print(f\"   NaN count before: {int(air_temp.isnull().sum())}\")\n", "print(f\"   NaN count after: {int(clean_data.isnull().sum())}\")\n", "\n", "# Broadcasting example\n", "print(f\"\\n📡 Broadcasting example:\")\n", "# Create a 1D array for each latitude\n", "lat_weights = np.cos(np.deg2rad(air_temp.lat))\n", "lat_weights_da = xr.<PERSON>(lat_weights, dims=['lat'], coords={'lat': air_temp.lat})\n", "\n", "# Broadcast multiplication\n", "weighted_temp = air_temp * lat_weights_da\n", "print(f\"   Original temp shape: {air_temp.shape}\")\n", "print(f\"   Lat weights shape: {lat_weights_da.shape}\")\n", "print(f\"   Weighted temp shape: {weighted_temp.shape}\")\n", "print(f\"   Broadcasting worked: {weighted_temp.shape == air_temp.shape}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---\n", "\n", "# 🔄 **Section 4: GroupBy Operations**\n", "\n", "Master the split-apply-combine workflow for advanced data analysis.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# GroupBy operations - split-apply-combine\n", "print(\"🔄 GroupBy Operations:\")\n", "\n", "# Group by month\n", "monthly_mean = air_temp.groupby('time.month').mean()\n", "print(f\"\\n📅 Monthly climatology:\")\n", "print(f\"   Shape: {monthly_mean.shape}\")\n", "print(f\"   Months: {monthly_mean.month.values}\")\n", "print(f\"   Warmest month globally: {int(monthly_mean.mean(dim=['lat', 'lon']).argmax() + 1)}\")\n", "print(f\"   Coldest month globally: {int(monthly_mean.mean(dim=['lat', 'lon']).argmin() + 1)}\")\n", "\n", "# Group by season\n", "seasonal_mean = air_temp.groupby('time.season').mean()\n", "print(f\"\\n🌱 Seasonal climatology:\")\n", "print(f\"   Shape: {seasonal_mean.shape}\")\n", "print(f\"   Seasons: {list(seasonal_mean.season.values)}\")\n", "\n", "# Calculate seasonal temperature range\n", "seasonal_range = seasonal_mean.max(dim='season') - seasonal_mean.min(dim='season')\n", "print(f\"   Max seasonal range: {float(seasonal_range.max()):.2f} K\")\n", "print(f\"   Min seasonal range: {float(seasonal_range.min()):.2f} K\")\n", "\n", "# Group by year\n", "annual_mean = air_temp.groupby('time.year').mean()\n", "print(f\"\\n📊 Annual means:\")\n", "print(f\"   Shape: {annual_mean.shape}\")\n", "print(f\"   Years: {annual_mean.year.values}\")\n", "print(f\"   Warmest year: {int(annual_mean.mean(dim=['lat', 'lon']).argmax() + annual_mean.year.values[0])}\")\n", "\n", "# Custom grouping - group by day of year\n", "daily_climatology = air_temp.groupby('time.dayofyear').mean()\n", "print(f\"\\n📅 Daily climatology:\")\n", "print(f\"   Shape: {daily_climatology.shape}\")\n", "print(f\"   Days of year: 1 to {daily_climatology.dayofyear.values.max()}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Advanced GroupBy operations\n", "print(\"🔬 Advanced GroupBy Operations:\")\n", "\n", "# Calculate anomalies using groupby\n", "monthly_anomalies = air_temp.groupby('time.month') - air_temp.groupby('time.month').mean()\n", "print(f\"\\n📊 Monthly anomalies:\")\n", "print(f\"   Shape: {monthly_anomalies.shape}\")\n", "print(f\"   Mean anomaly: {float(monthly_anomalies.mean()):.6f} K (should be ~0)\")\n", "print(f\"   Anomaly std: {float(monthly_anomalies.std()):.3f} K\")\n", "\n", "# Multiple statistics with groupby\n", "monthly_stats = air_temp.groupby('time.month').agg(['mean', 'std', 'min', 'max'])\n", "print(f\"\\n📈 Monthly statistics:\")\n", "print(f\"   Available stats: {list(monthly_stats.keys())}\")\n", "print(f\"   Mean shape: {monthly_stats['mean'].shape}\")\n", "\n", "# Quantiles by group\n", "monthly_quantiles = air_temp.groupby('time.month').quantile([0.1, 0.5, 0.9], dim='time')\n", "print(f\"\\n📊 Monthly quantiles:\")\n", "print(f\"   Shape: {monthly_quantiles.shape}\")\n", "print(f\"   Quantiles: {monthly_quantiles.quantile.values}\")\n", "\n", "# Custom aggregation function\n", "def temperature_range(x):\n", "    \"\"\"Calculate temperature range.\"\"\"\n", "    return x.max() - x.min()\n", "\n", "monthly_range = air_temp.groupby('time.month').apply(temperature_range)\n", "print(f\"\\n🌡️ Monthly temperature range:\")\n", "print(f\"   Shape: {monthly_range.shape}\")\n", "print(f\"   Max monthly range: {float(monthly_range.max()):.2f} K\")\n", "print(f\"   Min monthly range: {float(monthly_range.min()):.2f} K\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---\n", "\n", "# 📊 **Section 5: Plotting and Visualization**\n", "\n", "Xarray's built-in plotting capabilities make data visualization effortless.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Basic plotting with xarray\n", "print(\"📊 Xarray Plotting Examples:\")\n", "\n", "# 1D plot (time series)\n", "plt.figure(figsize=(15, 10))\n", "\n", "plt.subplot(2, 3, 1)\n", "# Global mean temperature time series\n", "global_temp = air_temp.mean(dim=['lat', 'lon'])\n", "global_temp.plot()\n", "plt.title('Global Mean Temperature')\n", "plt.ylabel('Temperature (K)')\n", "\n", "plt.subplot(2, 3, 2)\n", "# 2D plot (spatial map)\n", "air_temp.isel(time=0).plot(cmap='RdYlBu_r')\n", "plt.title('Temperature Map (First Time Step)')\n", "\n", "plt.subplot(2, 3, 3)\n", "# Contour plot\n", "air_temp.mean(dim='time').plot.contour(levels=10)\n", "plt.title('Temperature Contours (Time Mean)')\n", "\n", "plt.subplot(2, 3, 4)\n", "# Filled contour plot\n", "air_temp.mean(dim='time').plot.contourf(levels=15, cmap='viridis')\n", "plt.title('Filled Contours (Time Mean)')\n", "\n", "plt.subplot(2, 3, 5)\n", "# Histogram\n", "air_temp.plot.hist(bins=50, alpha=0.7)\n", "plt.title('Temperature Distribution')\n", "plt.xlabel('Temperature (K)')\n", "\n", "plt.subplot(2, 3, 6)\n", "# Line plot with multiple lines\n", "for lat_val in [75, 50, 25]:\n", "    temp_at_lat = air_temp.sel(lat=lat_val, method='nearest').mean(dim='lon')\n", "    temp_at_lat.plot(label=f'{lat_val}°N')\n", "plt.title('Temperature by La<PERSON><PERSON>')\n", "plt.legend()\n", "plt.ylabel('Temperature (K)')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"✅ Basic plotting examples completed\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Advanced plotting techniques\n", "print(\"🎨 Advanced Plotting Techniques:\")\n", "\n", "plt.figure(figsize=(15, 12))\n", "\n", "# Subplot 1: Seasonal comparison\n", "plt.subplot(2, 2, 1)\n", "seasonal_temp = air_temp.groupby('time.season').mean()\n", "for season in seasonal_temp.season.values:\n", "    seasonal_temp.sel(season=season).mean(dim='lon').plot(label=season)\n", "plt.title('Seasonal Temperature by La<PERSON><PERSON>')\n", "plt.xlabel('Latitude')\n", "plt.ylabel('Temperature (K)')\n", "plt.legend()\n", "\n", "# Subplot 2: Anomaly plot\n", "plt.subplot(2, 2, 2)\n", "temp_anomaly = air_temp - air_temp.mean(dim='time')\n", "temp_anomaly.isel(time=0).plot(cmap='RdBu_r', center=0)\n", "plt.title('Temperature Anomaly (First Time Step)')\n", "\n", "# Subplot 3: Zonal mean (latitude average)\n", "plt.subplot(2, 2, 3)\n", "zonal_mean = air_temp.mean(dim='lon')\n", "zonal_mean.plot(x='time', y='lat', cmap='RdYlBu_r')\n", "plt.title('Zonal Mean Temperature (Hovmöller)')\n", "\n", "# Subplot 4: Monthly climatology\n", "plt.subplot(2, 2, 4)\n", "monthly_clim = air_temp.groupby('time.month').mean()\n", "monthly_clim.mean(dim=['lat', 'lon']).plot(marker='o')\n", "plt.title('Monthly Temperature Climatology')\n", "plt.xlabel('Month')\n", "plt.ylabel('Temperature (K)')\n", "plt.xticks(range(1, 13))\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"✅ Advanced plotting examples completed\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Customized plotting with matplotlib integration\n", "print(\"🎯 Customized Plotting Examples:\")\n", "\n", "# Create a publication-quality figure\n", "fig, axes = plt.subplots(2, 2, figsize=(14, 10))\n", "fig.suptitle('Comprehensive Temperature Analysis', fontsize=16, fontweight='bold')\n", "\n", "# Plot 1: Global temperature trend\n", "ax1 = axes[0, 0]\n", "global_annual = air_temp.groupby('time.year').mean().mean(dim=['lat', 'lon'])\n", "global_annual.plot(ax=ax1, color='red', linewidth=2, marker='o', markersize=4)\n", "ax1.set_title('Global Annual Mean Temperature', fontweight='bold')\n", "ax1.set_ylabel('Temperature (K)')\n", "ax1.grid(True, alpha=0.3)\n", "\n", "# Plot 2: Latitude temperature profile\n", "ax2 = axes[0, 1]\n", "lat_profile = air_temp.mean(dim=['time', 'lon'])\n", "lat_profile.plot(ax=ax2, color='blue', linewidth=2)\n", "ax2.set_title('Zonal Mean Temperature Profile', fontweight='bold')\n", "ax2.set_xlabel('Latitude')\n", "ax2.set_ylabel('Temperature (K)')\n", "ax2.grid(True, alpha=0.3)\n", "\n", "# Plot 3: Temperature map with custom colormap\n", "ax3 = axes[1, 0]\n", "temp_map = air_temp.mean(dim='time')\n", "im = temp_map.plot(ax=ax3, cmap='plasma', add_colorbar=False)\n", "ax3.set_title('Mean Temperature Map', fontweight='bold')\n", "cbar = plt.colorbar(im, ax=ax3, shrink=0.8)\n", "cbar.set_label('Temperature (K)')\n", "\n", "# Plot 4: Seasonal cycle\n", "ax4 = axes[1, 1]\n", "seasonal_cycle = air_temp.groupby('time.month').mean().mean(dim=['lat', 'lon'])\n", "seasonal_cycle.plot(ax=ax4, color='green', linewidth=3, marker='s', markersize=6)\n", "ax4.set_title('Global Seasonal Cycle', fontweight='bold')\n", "ax4.set_xlabel('Month')\n", "ax4.set_ylabel('Temperature (K)')\n", "ax4.set_xticks(range(1, 13))\n", "ax4.grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"✅ Publication-quality plotting completed\")\n", "\n", "# Quick plotting tips\n", "print(\"\\n💡 Plotting Tips:\")\n", "print(\"   📊 Use .plot() for automatic plot type selection\")\n", "print(\"   🎨 Use .plot.contour(), .plot.contourf(), .plot.hist() for specific types\")\n", "print(\"   🌈 Specify cmap parameter for custom colormaps\")\n", "print(\"   📏 Use figsize parameter to control figure size\")\n", "print(\"   🎯 Use center=0 for diverging colormaps with anomalies\")\n", "print(\"   📍 Use add_colorbar=False to customize colorbar placement\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---\n", "\n", "# ⚡ **Section 6: Performance and Dask Integration**\n", "\n", "Learn to handle large datasets efficiently with chunking and parallel computing.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Working with Das<PERSON> for large datasets\n", "print(\"⚡ Dask Integration for Performance:\")\n", "\n", "# Create a chunked version of our data\n", "chunked_air = air_temp.chunk({'time': 100, 'lat': 10, 'lon': 20})\n", "\n", "print(f\"\\n📦 Chunking information:\")\n", "print(f\"   Original data type: {type(air_temp.data)}\")\n", "print(f\"   Chunked data type: {type(chunked_air.data)}\")\n", "print(f\"   Chunk sizes: {dict(chunked_air.chunks)}\")\n", "print(f\"   Number of chunks: {chunked_air.data.npartitions}\")\n", "\n", "# Lazy computation example\n", "print(f\"\\n🔄 Lazy computation example:\")\n", "lazy_mean = chunked_air.mean(dim='time')  # This doesn't compute yet\n", "print(f\"   Lazy operation created: {type(lazy_mean.data)}\")\n", "print(f\"   Memory usage: Minimal (computation not performed)\")\n", "\n", "# Trigger computation\n", "import time\n", "start_time = time.time()\n", "computed_mean = lazy_mean.compute()  # Now it computes\n", "computation_time = time.time() - start_time\n", "\n", "print(f\"   Computation completed in: {computation_time:.3f} seconds\")\n", "print(f\"   Result type: {type(computed_mean.data)}\")\n", "print(f\"   Result shape: {computed_mean.shape}\")\n", "\n", "# Memory-efficient operations\n", "print(f\"\\n💾 Memory-efficient operations:\")\n", "\n", "# Progressive computation\n", "progressive_stats = {\n", "    'mean': chunked_air.mean(),\n", "    'std': chunked_air.std(),\n", "    'min': chunked_air.min(),\n", "    'max': chunked_air.max()\n", "}\n", "\n", "print(f\"   Created {len(progressive_stats)} lazy operations\")\n", "\n", "# Compute all at once (more efficient)\n", "start_time = time.time()\n", "computed_stats = xr.Dataset(progressive_stats).compute()\n", "batch_time = time.time() - start_time\n", "\n", "print(f\"   Batch computation time: {batch_time:.3f} seconds\")\n", "print(f\"   Statistics computed: {list(computed_stats.data_vars)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---\n", "\n", "# 🎯 **Section 7: Real-world Examples and Best Practices**\n", "\n", "Practical examples that demonstrate xarray's power in scientific computing.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Real-world example: Climate analysis workflow\n", "print(\"🌍 Real-world Climate Analysis Workflow:\")\n", "\n", "# Step 1: Load and examine data\n", "print(\"\\n📊 Step 1: Data Exploration\")\n", "print(f\"   Dataset shape: {air_temp.shape}\")\n", "print(f\"   Time span: {air_temp.time.values[0]} to {air_temp.time.values[-1]}\")\n", "print(f\"   Spatial coverage: {float(air_temp.lat.min()):.1f}° to {float(air_temp.lat.max()):.1f}°N\")\n", "print(f\"   Data completeness: {(1 - float(air_temp.isnull().sum() / air_temp.size)) * 100:.1f}%\")\n", "\n", "# Step 2: Quality control\n", "print(\"\\n🔍 Step 2: Quality Control\")\n", "# Check for reasonable temperature range\n", "temp_min, temp_max = float(air_temp.min()), float(air_temp.max())\n", "reasonable_range = (200 <= temp_min <= 320) and (200 <= temp_max <= 320)\n", "print(f\"   Temperature range: {temp_min:.1f} to {temp_max:.1f} K\")\n", "print(f\"   Range check: {'✅ PASS' if reasonable_range else '❌ FAIL'}\")\n", "\n", "# Check for missing data patterns\n", "missing_by_time = air_temp.isnull().sum(dim=['lat', 'lon'])\n", "max_missing = int(missing_by_time.max())\n", "print(f\"   Max missing values per time step: {max_missing}\")\n", "print(f\"   Missing data check: {'✅ PASS' if max_missing == 0 else '⚠️ WARNING'}\")\n", "\n", "# Step 3: Calculate climate indices\n", "print(\"\\n📈 Step 3: Climate Indices Calculation\")\n", "\n", "# Global mean temperature\n", "# Weight by latitude (cosine weighting)\n", "weights = np.cos(np.deg2rad(air_temp.lat))\n", "weights.name = \"weights\"\n", "air_weighted = air_temp.weighted(weights)\n", "global_mean = air_weighted.mean(dim=['lat', 'lon'])\n", "\n", "print(f\"   Global mean temperature: {float(global_mean.mean()):.2f} K\")\n", "print(f\"   Global temperature trend: {float(global_mean[-365:].mean() - global_mean[:365].mean()):.3f} K/period\")\n", "\n", "# Temperature anomalies\n", "climatology = air_temp.groupby('time.dayofyear').mean()\n", "anomalies = air_temp.groupby('time.dayofyear') - climatology\n", "print(f\"   Anomaly std deviation: {float(anomalies.std()):.3f} K\")\n", "\n", "# Extreme temperature events\n", "threshold_hot = air_temp.quantile(0.95)\n", "threshold_cold = air_temp.quantile(0.05)\n", "hot_days = (air_temp > threshold_hot).sum()\n", "cold_days = (air_temp < threshold_cold).sum()\n", "\n", "print(f\"   Hot extreme threshold: {float(threshold_hot):.1f} K\")\n", "print(f\"   Cold extreme threshold: {float(threshold_cold):.1f} K\")\n", "print(f\"   Total hot extreme events: {int(hot_days)}\")\n", "print(f\"   Total cold extreme events: {int(cold_days)}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Advanced analysis: Regional climate patterns\n", "print(\"🗺️ Advanced Regional Climate Analysis:\")\n", "\n", "# Define regions of interest\n", "regions = {\n", "    'Arctic': {'lat': slice(70, 90), 'lon': slice(0, 360)},\n", "    'Tropics': {'lat': slice(-23.5, 23.5), 'lon': slice(0, 360)},\n", "    'North_America': {'lat': slice(25, 70), 'lon': slice(235, 295)},\n", "    'Europe': {'lat': slice(35, 70), 'lon': slice(350, 40)}\n", "}\n", "\n", "regional_stats = {}\n", "\n", "for region_name, bounds in regions.items():\n", "    # Extract regional data\n", "    if bounds['lon'].start > bounds['lon'].stop:  # Handle longitude wraparound\n", "        region_data = air_temp.sel(\n", "            lat=bounds['lat'],\n", "            lon=xr.concat([\n", "                air_temp.sel(lon=slice(bounds['lon'].start, 360)),\n", "                air_temp.sel(lon=slice(0, bounds['lon'].stop))\n", "            ], dim='lon').lon\n", "        )\n", "    else:\n", "        region_data = air_temp.sel(lat=bounds['lat'], lon=bounds['lon'])\n", "    \n", "    # Calculate regional statistics\n", "    regional_mean = region_data.mean(dim=['lat', 'lon'])\n", "    regional_stats[region_name] = {\n", "        'mean_temp': float(regional_mean.mean()),\n", "        'temp_std': float(regional_mean.std()),\n", "        'seasonal_range': float(regional_mean.groupby('time.season').mean().max() - \n", "                               regional_mean.groupby('time.season').mean().min()),\n", "        'data_points': int(region_data.size)\n", "    }\n", "\n", "print(\"\\n📊 Regional Climate Statistics:\")\n", "for region, stats in regional_stats.items():\n", "    print(f\"\\n   🌍 {region}:\")\n", "    print(f\"      Mean temperature: {stats['mean_temp']:.2f} K\")\n", "    print(f\"      Temperature variability: {stats['temp_std']:.3f} K\")\n", "    print(f\"      Seasonal range: {stats['seasonal_range']:.2f} K\")\n", "    print(f\"      Data points: {stats['data_points']:,}\")\n", "\n", "# Compare regions\n", "warmest_region = max(regional_stats.keys(), key=lambda x: regional_stats[x]['mean_temp'])\n", "coldest_region = min(regional_stats.keys(), key=lambda x: regional_stats[x]['mean_temp'])\n", "most_variable = max(regional_stats.keys(), key=lambda x: regional_stats[x]['seasonal_range'])\n", "\n", "print(f\"\\n🏆 Regional Comparisons:\")\n", "print(f\"   🔥 Warmest region: {warmest_region}\")\n", "print(f\"   🧊 Coldest region: {coldest_region}\")\n", "print(f\"   🌡️ Most seasonal variation: {most_variable}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Best practices and performance tips\n", "print(\"💡 Xarray Best Practices and Performance Tips:\")\n", "\n", "# Demonstrate efficient vs inefficient operations\n", "print(\"\\n⚡ Performance Comparison:\")\n", "\n", "# Inefficient: Multiple separate operations\n", "start_time = time.time()\n", "inefficient_mean = air_temp.mean()\n", "inefficient_std = air_temp.std()\n", "inefficient_min = air_temp.min()\n", "inefficient_max = air_temp.max()\n", "inefficient_time = time.time() - start_time\n", "\n", "# Efficient: Batch operations\n", "start_time = time.time()\n", "efficient_stats = xr.Dataset({\n", "    'mean': air_temp.mean(),\n", "    'std': air_temp.std(),\n", "    'min': air_temp.min(),\n", "    'max': air_temp.max()\n", "}).compute()\n", "efficient_time = time.time() - start_time\n", "\n", "print(f\"   Inefficient approach: {inefficient_time:.3f} seconds\")\n", "print(f\"   Efficient approach: {efficient_time:.3f} seconds\")\n", "print(f\"   Speedup: {inefficient_time/efficient_time:.1f}x faster\")\n", "\n", "# Memory usage tips\n", "print(f\"\\n💾 Memory Management Tips:\")\n", "print(f\"   Original data size: {air_temp.nbytes / 1e6:.1f} MB\")\n", "\n", "# Chunked version\n", "chunked_version = air_temp.chunk({'time': 100})\n", "print(f\"   Chunked version memory: ~{chunked_version.nbytes / chunked_version.data.npartitions / 1e6:.1f} MB per chunk\")\n", "\n", "# Demonstrate lazy evaluation\n", "lazy_operation = chunked_version.rolling(time=30).mean()\n", "print(f\"   Lazy operation created: {type(lazy_operation.data)}\")\n", "print(f\"   Memory usage: Minimal until .compute() is called\")\n", "\n", "print(f\"\\n🎯 Key Best Practices:\")\n", "best_practices = [\n", "    \"Use .sel() with coordinate values instead of .isel() when possible\",\n", "    \"Leverage broadcasting - xarray handles dimension alignment automatically\",\n", "    \"Use .groupby() for split-apply-combine operations\",\n", "    \"Chunk large datasets with .chunk() for memory efficiency\",\n", "    \"Batch computations with xr.Dataset() and .compute() for performance\",\n", "    \"Use .weighted() for proper area-weighted averages\",\n", "    \"Specify dimensions explicitly in reduction operations\",\n", "    \"Use .attrs to document your data and operations\",\n", "    \"Leverage x<PERSON>y's plotting for quick visualization\",\n", "    \"Use .apply_ufunc() to integrate with other libraries\"\n", "]\n", "\n", "for i, practice in enumerate(best_practices, 1):\n", "    print(f\"   {i:2d}. {practice}\")\n", "\n", "print(f\"\\n🎓 Congratulations! You've mastered xarray fundamentals!\")\n", "print(f\"   📚 You can now handle multi-dimensional scientific data efficiently\")\n", "print(f\"   🔬 You understand indexing, computation, and visualization patterns\")\n", "print(f\"   ⚡ You know how to optimize performance for large datasets\")\n", "print(f\"   🌍 You can apply these skills to real-world scientific problems\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---\n", "\n", "# 🎓 **Xarray Mastery Summary**\n", "\n", "## ✅ **Concepts Mastered:**\n", "\n", "### **1. Data Structures**\n", "- DataArray: N-dimensional arrays with labeled dimensions\n", "- Dataset: Dictionary-like container of DataArrays\n", "- Coordinates: Labels for array dimensions\n", "- Attributes: Metadata storage\n", "\n", "### **2. Indexing & Selection**\n", "- Position-based indexing with `.isel()`\n", "- Label-based indexing with `.sel()`\n", "- Nearest neighbor lookups\n", "- DateTime indexing and slicing\n", "- Boolean indexing\n", "\n", "### **3. Computation**\n", "- Arithmetic operations with automatic alignment\n", "- Reduction operations along named dimensions\n", "- Broadcasting across different dimensions\n", "- Custom functions with `apply_ufunc`\n", "- Weighted operations\n", "\n", "### **4. GroupBy Operations**\n", "- Split-apply-combine workflows\n", "- Temporal grouping (month, season, year)\n", "- Custom grouping functions\n", "- Anomaly calculations\n", "\n", "### **5. Visualization**\n", "- Automatic plot type selection\n", "- 1D, 2D, and multi-dimensional plotting\n", "- Integration with matplotlib\n", "- Custom colormaps and styling\n", "\n", "### **6. Performance**\n", "- Dask integration for large datasets\n", "- Chunking strategies\n", "- Lazy evaluation\n", "- Memory-efficient operations\n", "\n", "## 🚀 **Next Steps:**\n", "\n", "- **Advanced Topics**: Interpolation, resampling, merging datasets\n", "- **I/O Operations**: Reading/writing NetCDF, Zarr, HDF5 files\n", "- **Integration**: Working with pandas, numpy, dask, mat<PERSON><PERSON><PERSON>b\n", "- **Domain Applications**: Climate science, oceanography, atmospheric science\n", "\n", "## 📚 **Resources:**\n", "\n", "- [Xarray Documentation](https://docs.xarray.dev/)\n", "- [Xarray Tutorial Repository](https://github.com/xarray-contrib/xarray-tutorial)\n", "- [Pangeo Community](https://pangeo.io/)\n", "- [Scientific Python Ecosystem](https://scientific-python.org/)\n", "\n", "---\n", "\n", "**🎉 You're now ready to tackle real-world scientific data analysis with xarray!**\n"]}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}