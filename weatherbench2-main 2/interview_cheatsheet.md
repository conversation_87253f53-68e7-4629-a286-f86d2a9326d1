# 🚀 WeatherBench2 Interview Cheatsheet

## 📚 **Quick Reference for Weather Data Analysis & Python Coding**

---

## 🐍 **Python Essentials**

### **Data Structures - Choose the Right Tool**
```python
# Lists - Dynamic arrays, ordered
temps = [20, 22, 21, 23]
filtered = [t for t in temps if t > 21]  # List comprehension

# Dictionaries - Key-value pairs
station_data = {'NYC': 22.5, 'LA': 28.1}
result = {k: v*9/5+32 for k, v in station_data.items()}  # Dict comprehension

# Sets - Unique values, fast lookup
conditions = {'sunny', 'cloudy', 'rainy'}
common = set1 & set2  # Intersection

# Tuples - Immutable, coordinates
coords = (40.7, -74.0)  # lat, lon
lat, lon = coords  # Unpacking
```

### **Advanced Collections**
```python
from collections import defaultdict, Counter, deque

# defaultdict - No KeyError
groups = defaultdict(list)
groups['station_A'].append(reading)  # Auto-creates list

# Counter - Frequency counting
conditions = Counter(['sunny', 'cloudy', 'sunny'])
most_common = conditions.most_common(1)  # [('sunny', 2)]

# deque - Efficient queues
buffer = deque(maxlen=100)  # Sliding window
buffer.append(new_data)  # Auto-removes oldest
```

### **Object-Oriented Programming**
```python
# Dataclasses (Modern Python)
@dataclasses.dataclass
class WeatherReading:
    station: str
    temp: float
    time: str
    
    def to_fahrenheit(self) -> float:
        return self.temp * 9/5 + 32

# Abstract Base Classes
from abc import ABC, abstractmethod

class Metric(ABC):
    @abstractmethod
    def compute(self, forecast, obs) -> float:
        pass

class RMSE(Metric):
    def compute(self, forecast, obs) -> float:
        return np.sqrt(np.mean((forecast - obs)**2))
```

### **Error Handling**
```python
# Robust parsing
def parse_reading(line):
    try:
        parts = line.strip().split(',')
        return {
            'temp': float(parts[1]),
            'station': parts[0]
        }
    except (ValueError, IndexError) as e:
        print(f"Parse error: {e}")
        return None

# Context managers
with open('data.csv') as f:
    data = f.read()  # Auto-closes file
```

---

## 📊 **Xarray Fundamentals**

### **Core Operations**
```python
import xarray as xr

# Load data
ds = xr.open_dataset('weather.nc')
temp = ds['temperature']

# Indexing
temp.sel(lat=40, lon=-74, method='nearest')  # Label-based
temp.isel(time=0)  # Position-based
temp.sel(time='2023-01-01')  # Date selection

# Computations
temp_celsius = temp - 273.15  # Element-wise
daily_mean = temp.resample(time='D').mean()  # Resampling
spatial_mean = temp.mean(dim=['lat', 'lon'])  # Reduction

# GroupBy operations
monthly = temp.groupby('time.month').mean()
anomalies = temp.groupby('time.month') - monthly
```

### **Essential Patterns**
```python
# Area-weighted averaging
weights = np.cos(np.deg2rad(temp.lat))
weighted_mean = temp.weighted(weights).mean()

# Interpolation
regridded = temp.interp(lat=new_lats, lon=new_lons)

# Plotting
temp.plot()  # Auto-detects plot type
temp.plot.contourf(levels=20)  # Filled contours
```

---

## 🌍 **Spatial & Geospatial**

### **Distance Calculations**
```python
def haversine_distance(lat1, lon1, lat2, lon2):
    """Great circle distance in km"""
    R = 6371  # Earth radius
    dlat = np.radians(lat2 - lat1)
    dlon = np.radians(lon2 - lon1)
    a = (np.sin(dlat/2)**2 + 
         np.cos(np.radians(lat1)) * np.cos(np.radians(lat2)) * 
         np.sin(dlon/2)**2)
    return R * 2 * np.arcsin(np.sqrt(a))
```

### **Coordinate Systems**
```python
# Convert longitude conventions
def lon_360_to_180(lon):
    return ((lon + 180) % 360) - 180

# Grid cell areas (for proper averaging)
def grid_cell_area(lat, dlat, dlon):
    R = 6371000  # Earth radius in meters
    lat_rad = np.radians(lat)
    dlat_rad = np.radians(dlat)
    dlon_rad = np.radians(dlon)
    return R**2 * np.abs(np.sin(lat_rad + dlat_rad/2) - 
                         np.sin(lat_rad - dlat_rad/2)) * np.abs(dlon_rad)
```

### **Spatial Indexing**
```python
from scipy.spatial import cKDTree

# Fast nearest neighbor search
coords = np.column_stack([lats.flatten(), lons.flatten()])
tree = cKDTree(coords)
distances, indices = tree.query([query_lat, query_lon], k=5)
```

---

## 🔧 **Performance Patterns**

### **Efficient Loops & Comprehensions**
```python
# Good: List comprehension
result = [process(x) for x in data if condition(x)]

# Good: Generator for memory efficiency
def process_stream(data):
    for item in data:
        if condition(item):
            yield process(item)

# Good: Built-in functions
total = sum(values)
maximum = max(values)
```

### **Vectorization with NumPy**
```python
# Bad: Python loop
result = []
for i in range(len(temps)):
    result.append(temps[i] * 9/5 + 32)

# Good: Vectorized
result = temps * 9/5 + 32  # NumPy array operation
```

### **Memory Management**
```python
# Use generators for large datasets
def read_large_file(filename):
    with open(filename) as f:
        for line in f:
            yield process_line(line)

# Chunking for big data
for chunk in pd.read_csv('huge_file.csv', chunksize=10000):
    process_chunk(chunk)
```

---

## 🌤️ **Weather-Specific Patterns**

### **Common Calculations**
```python
# Temperature conversions
celsius_to_fahrenheit = lambda c: c * 9/5 + 32
kelvin_to_celsius = lambda k: k - 273.15

# Wind speed from components
wind_speed = np.sqrt(u_component**2 + v_component**2)

# Relative humidity (simplified)
def relative_humidity(temp_c, dewpoint_c):
    return 100 * np.exp((17.625 * dewpoint_c) / (243.04 + dewpoint_c)) / \
           np.exp((17.625 * temp_c) / (243.04 + temp_c))
```

### **Data Quality Checks**
```python
def validate_temperature(temp):
    return -90 <= temp <= 60  # Reasonable Earth temperature range

def validate_pressure(pressure):
    return 800 <= pressure <= 1200  # hPa range

def validate_humidity(humidity):
    return 0 <= humidity <= 100  # Percentage
```

### **Time Series Operations**
```python
# Seasonal decomposition
def simple_seasonal_decompose(ts, period=365):
    trend = ts.rolling(window=period, center=True).mean()
    detrended = ts - trend
    seasonal = detrended.groupby(detrended.index.dayofyear).mean()
    residual = ts - trend - seasonal
    return trend, seasonal, residual

# Anomaly detection
def detect_anomalies(data, threshold=2):
    mean = data.mean()
    std = data.std()
    return np.abs(data - mean) > threshold * std
```

---

## 🎯 **Interview Problem Patterns**

### **Data Processing Pipeline**
```python
def process_weather_data(raw_data):
    # 1. Parse and validate
    parsed = [parse_reading(line) for line in raw_data]
    valid = [r for r in parsed if r is not None]
    
    # 2. Group by station
    by_station = defaultdict(list)
    for reading in valid:
        by_station[reading['station']].append(reading)
    
    # 3. Calculate statistics
    stats = {}
    for station, readings in by_station.items():
        temps = [r['temp'] for r in readings]
        stats[station] = {
            'mean': sum(temps) / len(temps),
            'min': min(temps),
            'max': max(temps),
            'count': len(temps)
        }
    
    return stats
```

### **Sliding Window Analysis**
```python
def moving_average(data, window_size):
    result = []
    for i in range(len(data) - window_size + 1):
        window = data[i:i + window_size]
        result.append(sum(window) / len(window))
    return result

# Or with deque for efficiency
def moving_average_efficient(data, window_size):
    window = deque(maxlen=window_size)
    result = []
    for value in data:
        window.append(value)
        if len(window) == window_size:
            result.append(sum(window) / len(window))
    return result
```

### **Finding Patterns**
```python
def find_temperature_trends(temps, min_length=3):
    """Find increasing temperature sequences"""
    trends = []
    current_trend = [temps[0]]
    
    for i in range(1, len(temps)):
        if temps[i] > temps[i-1]:
            current_trend.append(temps[i])
        else:
            if len(current_trend) >= min_length:
                trends.append(current_trend.copy())
            current_trend = [temps[i]]
    
    # Check final trend
    if len(current_trend) >= min_length:
        trends.append(current_trend)
    
    return trends
```

---

## 💡 **Quick Tips**

### **Code Style**
- Use list comprehensions instead of loops when possible
- Choose descriptive variable names: `temperature` not `t`
- Handle errors explicitly with try/except
- Use type hints for better code documentation

### **Performance**
- Use NumPy for numerical operations
- Use generators for large datasets
- Avoid nested loops when possible
- Use built-in functions (sum, max, min)

### **Weather Data**
- Always validate data ranges
- Handle missing data gracefully
- Use proper coordinate systems
- Consider Earth's spherical geometry

### **Interview Strategy**
1. **Clarify requirements** - Ask about edge cases
2. **Start simple** - Get basic solution working first
3. **Optimize later** - Improve performance after correctness
4. **Test thoroughly** - Consider boundary conditions
5. **Explain your thinking** - Walk through your approach

---

## 🔗 **Key Libraries**
```python
import numpy as np           # Numerical computing
import pandas as pd          # Data manipulation
import xarray as xr          # Multi-dimensional arrays
import matplotlib.pyplot as plt  # Plotting
from collections import defaultdict, Counter, deque
import dataclasses          # Modern data classes
from typing import List, Dict, Optional  # Type hints
```

**🎯 Remember: Practice these patterns until they become second nature!**
