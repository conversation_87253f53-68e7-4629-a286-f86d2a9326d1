{"cells": [{"cell_type": "markdown", "metadata": {"id": "header"}, "source": ["# WeatherBench2 Complete Tutorial: Master All Core Concepts\n", "\n", "## 🌍 **Comprehensive Guide to WeatherBench2 Framework**\n", "\n", "This tutorial covers **ALL** the key concepts, patterns, and techniques used in WeatherBench2, based on the actual codebase analysis. Perfect for understanding modern weather forecasting evaluation frameworks.\n", "\n", "---\n", "\n", "## 📚 **What You'll Learn**\n", "\n", "1. **Data Schema & Structure** - How WeatherBench2 organizes weather data\n", "2. **Configuration Management** - Dataclass-based config patterns\n", "3. **Evaluation Framework** - Metrics, regions, and evaluation patterns\n", "4. **Derived Variables** - Computing new variables from existing data\n", "5. **Distributed Processing** - Apache Beam and xarray-beam patterns\n", "6. **Performance Optimization** - Chunking, rechunking, and memory management\n", "7. **Testing Patterns** - Comprehensive testing with mock data\n", "8. **Visualization** - Weather data plotting and analysis\n", "9. **Advanced Techniques** - JAX integration, caching, and optimization\n", "10. **Real-world Applications** - Complete workflow examples\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "setup"}, "outputs": [], "source": ["# Essential WeatherBench2 imports - memorize these patterns\n", "import dataclasses\n", "import typing as t\n", "from typing import Optional, Dict, List, Tuple, Sequence, Union, Any, Callable\n", "\n", "import numpy as np\n", "import pandas as pd\n", "import xarray as xr\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "# Scientific computing\n", "from datetime import datetime, timedelta\n", "import functools\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Install required packages\n", "!pip install xarray netcdf4 dask matplotlib seaborn cartopy -q\n", "\n", "print(\"✅ WeatherBench2 environment ready!\")\n", "print(\"📚 This tutorial covers ALL WeatherBench2 concepts from the actual codebase\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---\n", "\n", "# 📊 **Section 1: WeatherBench2 Data Schema & Structure**\n", "\n", "Understanding how WeatherBench2 organizes and structures weather data is fundamental to everything else.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# WeatherBench2 <PERSON><PERSON><PERSON> - from weatherbench2/schema.py\n", "\n", "# Standard variable names used throughout WeatherBench2\n", "ALL_3D_VARIABLES = (\n", "    'geopotential',\n", "    'temperature', \n", "    'u_component_of_wind',\n", "    'v_component_of_wind',\n", "    'specific_humidity',\n", ")\n", "\n", "ALL_2D_VARIABLES = (\n", "    '2m_temperature',\n", "    '10m_u_component_of_wind',\n", "    '10m_v_component_of_wind', \n", "    'mean_sea_level_pressure',\n", "    'total_precipitation_6hr',\n", ")\n", "\n", "# Standard pressure levels (hPa)\n", "PRESSURE_LEVELS = [50, 100, 150, 200, 250, 300, 400, 500, 600, 700, 850, 925, 1000]\n", "\n", "def mock_truth_data(\n", "    variables_3d: Sequence[str] = ALL_3D_VARIABLES,\n", "    variables_2d: Sequence[str] = ALL_2D_VARIABLES,\n", "    time_start: str = '2020-01-01',\n", "    time_stop: str = '2020-12-31',\n", "    spatial_resolution_deg: float = 1.0,\n", "    levels: Sequence[int] = PRESSURE_LEVELS\n", ") -> xr.Dataset:\n", "    \"\"\"\n", "    Create mock truth data following WeatherBench2 schema conventions.\n", "    \n", "    This mimics the schema.mock_truth_data function from WeatherBench2.\n", "    \"\"\"\n", "    # Time coordinate - daily data\n", "    time = pd.date_range(time_start, time_stop, freq='D')\n", "    \n", "    # Spatial coordinates (global coverage)\n", "    latitude = np.arange(-90, 90 + spatial_resolution_deg, spatial_resolution_deg)\n", "    longitude = np.arange(0, 360, spatial_resolution_deg)\n", "    \n", "    # Pressure levels\n", "    level = np.array(levels)\n", "    \n", "    # Initialize dataset\n", "    data_vars = {}\n", "    \n", "    # Add 3D variables (time, level, lat, lon)\n", "    for var in variables_3d:\n", "        shape = (len(time), len(level), len(latitude), len(longitude))\n", "        data = np.random.randn(*shape)\n", "        \n", "        # Add realistic scaling based on variable type\n", "        if 'temperature' in var:\n", "            data = data * 10 + 273.15  # <PERSON><PERSON>\n", "        elif 'wind' in var:\n", "            data = data * 5  # m/s\n", "        elif 'geopotential' in var:\n", "            data = data * 1000 + 50000  # m²/s²\n", "        elif 'humidity' in var:\n", "            data = np.abs(data) * 0.01  # kg/kg\n", "            \n", "        data_vars[var] = (['time', 'level', 'latitude', 'longitude'], data)\n", "    \n", "    # Add 2D variables (time, lat, lon)\n", "    for var in variables_2d:\n", "        shape = (len(time), len(latitude), len(longitude))\n", "        data = np.random.randn(*shape)\n", "        \n", "        # Add realistic scaling\n", "        if '2m_temperature' in var:\n", "            data = data * 15 + 288.15  # <PERSON><PERSON>\n", "        elif 'wind' in var:\n", "            data = data * 3  # m/s\n", "        elif 'pressure' in var:\n", "            data = data * 1000 + 101325  # Pa\n", "        elif 'precipitation' in var:\n", "            data = np.abs(data) * 0.001  # m\n", "            \n", "        data_vars[var] = (['time', 'latitude', 'longitude'], data)\n", "    \n", "    # Create dataset\n", "    ds = xr.Dataset(\n", "        data_vars,\n", "        coords={\n", "            'time': time,\n", "            'level': level,\n", "            'latitude': latitude,\n", "            'longitude': longitude,\n", "        },\n", "        attrs={\n", "            'title': 'WeatherBench2 Mock Truth Data',\n", "            'source': 'Synthetic data following WB2 schema',\n", "            'spatial_resolution': f'{spatial_resolution_deg}°',\n", "            'temporal_resolution': 'daily'\n", "        }\n", "    )\n", "    \n", "    return ds\n", "\n", "def mock_forecast_data(\n", "    variables_3d: Sequence[str] = ALL_3D_VARIABLES,\n", "    variables_2d: Sequence[str] = ALL_2D_VARIABLES,\n", "    init_times: Sequence[str] = ['2020-01-01', '2020-01-02'],\n", "    lead_times: Sequence[str] = ['1D', '2D', '3D', '5D', '7D'],\n", "    spatial_resolution_deg: float = 1.0,\n", "    levels: Sequence[int] = PRESSURE_LEVELS,\n", "    ensemble_size: int = 10\n", ") -> xr.Dataset:\n", "    \"\"\"\n", "    Create mock forecast data with ensemble members.\n", "    \n", "    This follows WeatherBench2 forecast data conventions.\n", "    \"\"\"\n", "    # Convert lead times to <PERSON><PERSON><PERSON>\n", "    lead_time_deltas = [pd.Timedelta(lt) for lt in lead_times]\n", "    \n", "    # Initialize coordinates\n", "    init_time = pd.to_datetime(init_times)\n", "    lead_time = pd.to_<PERSON><PERSON><PERSON>(lead_times)\n", "    latitude = np.arange(-90, 90 + spatial_resolution_deg, spatial_resolution_deg)\n", "    longitude = np.arange(0, 360, spatial_resolution_deg)\n", "    level = np.array(levels)\n", "    realization = np.arange(ensemble_size)\n", "    \n", "    # Create forecast dataset similar to truth data but with additional dimensions\n", "    data_vars = {}\n", "    \n", "    # Add 3D variables (init_time, lead_time, realization, level, lat, lon)\n", "    for var in variables_3d:\n", "        shape = (len(init_time), len(lead_time), len(realization), len(level), len(latitude), len(longitude))\n", "        data = np.random.randn(*shape)\n", "        \n", "        # Apply same scaling as truth data\n", "        if 'temperature' in var:\n", "            data = data * 10 + 273.15\n", "        elif 'wind' in var:\n", "            data = data * 5\n", "        elif 'geopotential' in var:\n", "            data = data * 1000 + 50000\n", "        elif 'humidity' in var:\n", "            data = np.abs(data) * 0.01\n", "            \n", "        data_vars[var] = (['init_time', 'lead_time', 'realization', 'level', 'latitude', 'longitude'], data)\n", "    \n", "    # Add 2D variables\n", "    for var in variables_2d:\n", "        shape = (len(init_time), len(lead_time), len(realization), len(latitude), len(longitude))\n", "        data = np.random.randn(*shape)\n", "        \n", "        if '2m_temperature' in var:\n", "            data = data * 15 + 288.15\n", "        elif 'wind' in var:\n", "            data = data * 3\n", "        elif 'pressure' in var:\n", "            data = data * 1000 + 101325\n", "        elif 'precipitation' in var:\n", "            data = np.abs(data) * 0.001\n", "            \n", "        data_vars[var] = (['init_time', 'lead_time', 'realization', 'latitude', 'longitude'], data)\n", "    \n", "    # Create forecast dataset\n", "    ds = xr.Dataset(\n", "        data_vars,\n", "        coords={\n", "            'init_time': init_time,\n", "            'lead_time': lead_time,\n", "            'realization': realization,\n", "            'level': level,\n", "            'latitude': latitude,\n", "            'longitude': longitude,\n", "        },\n", "        attrs={\n", "            'title': 'WeatherBench2 Mock Forecast Data',\n", "            'source': 'Synthetic ensemble forecast data',\n", "            'ensemble_size': ensemble_size,\n", "            'spatial_resolution': f'{spatial_resolution_deg}°'\n", "        }\n", "    )\n", "    \n", "    return ds\n", "\n", "# Create sample datasets\n", "print(\"🌍 Creating WeatherBench2 Schema Examples...\")\n", "truth_data = mock_truth_data(time_start='2020-01-01', time_stop='2020-01-31', spatial_resolution_deg=2.0)\n", "forecast_data = mock_forecast_data(init_times=['2020-01-01', '2020-01-02'], spatial_resolution_deg=2.0)\n", "\n", "print(\"\\n📊 Truth Data Schema:\")\n", "print(truth_data)\n", "print(\"\\n🔮 Forecast Data Schema:\")\n", "print(forecast_data)\n", "print(f\"\\n📏 Truth data size: {truth_data.nbytes / 1e6:.1f} MB\")\n", "print(f\"📏 Forecast data size: {forecast_data.nbytes / 1e6:.1f} MB\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---\n", "\n", "# ⚙️ **Section 2: Configuration Management (WeatherBench2 Style)**\n", "\n", "WeatherBench2 uses dataclasses extensively for configuration. This is a key pattern throughout the codebase.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# WeatherBench2 Configuration Patterns - from weatherbench2/config.py\n", "\n", "@dataclasses.dataclass\n", "class Selection:\n", "    \"\"\"Select a sub-set of forecast and truth data.\n", "    \n", "    This is the core data selection pattern used throughout WeatherBench2.\n", "    \"\"\"\n", "    variables: t.Sequence[str]\n", "    time_slice: slice\n", "    levels: t.Optional[t.Sequence[int]] = None\n", "    lat_slice: t.Optional[slice] = dataclasses.field(\n", "        default_factory=lambda: slice(None, None)\n", "    )\n", "    lon_slice: t.Optional[slice] = dataclasses.field(\n", "        default_factory=lambda: slice(None, None)\n", "    )\n", "    aux_variables: t.Optional[t.Sequence[str]] = None\n", "\n", "@dataclasses.dataclass\n", "class Paths:\n", "    \"\"\"Configuration for input and output paths.\"\"\"\n", "    forecast: str\n", "    obs: str\n", "    output_dir: str\n", "    output_file_prefix: t.Optional[str] = ''\n", "    climatology: t.Op<PERSON>[str] = None\n", "\n", "@dataclasses.dataclass\n", "class Data:\n", "    \"\"\"Data configuration class combining Selection and Paths.\"\"\"\n", "    selection: Selection\n", "    paths: Paths\n", "    by_init: t.Optional[bool] = True\n", "    rename_variables: t.Optional[t.Dict[str, str]] = None\n", "    pressure_level_suffixes: t.Optional[bool] = False\n", "\n", "def apply_selection(dataset: xr.Dataset, selection: Selection) -> xr.Dataset:\n", "    \"\"\"\n", "    Apply data selection following WeatherBench2 patterns.\n", "    \n", "    This is how WeatherBench2 handles data subsetting throughout the codebase.\n", "    \"\"\"\n", "    # Variable selection\n", "    if selection.variables:\n", "        dataset = dataset[selection.variables]\n", "    \n", "    # Time selection\n", "    if selection.time_slice:\n", "        dataset = dataset.sel(time=selection.time_slice)\n", "    \n", "    # Level selection (for 3D variables)\n", "    if selection.levels and 'level' in dataset.dims:\n", "        dataset = dataset.sel(level=selection.levels)\n", "    \n", "    # Spatial selection\n", "    if selection.lat_slice and selection.lat_slice != slice(None, None):\n", "        dataset = dataset.sel(latitude=selection.lat_slice)\n", "    if selection.lon_slice and selection.lon_slice != slice(None, None):\n", "        dataset = dataset.sel(longitude=selection.lon_slice)\n", "    \n", "    return dataset\n", "\n", "# Example usage of WeatherBench2 configuration patterns\n", "print(\"⚙️ WeatherBench2 Configuration Examples:\")\n", "\n", "# Create a selection configuration\n", "selection_config = Selection(\n", "    variables=['2m_temperature', 'total_precipitation_6hr'],\n", "    time_slice=slice('2020-01-01', '2020-01-15'),\n", "    lat_slice=slice(30, 60),  # Northern mid-latitudes\n", "    lon_slice=slice(0, 30)    # Europe region\n", ")\n", "\n", "# Apply selection to truth data\n", "selected_data = apply_selection(truth_data, selection_config)\n", "\n", "print(f\"\\n📊 Original data shape: {dict(truth_data.dims)}\")\n", "print(f\"📊 Selected data shape: {dict(selected_data.dims)}\")\n", "print(f\"📊 Selected variables: {list(selected_data.data_vars)}\")\n", "print(f\"📊 Time range: {selected_data.time.min().values} to {selected_data.time.max().values}\")\n", "print(f\"📊 Lat range: {selected_data.latitude.min().values:.1f}° to {selected_data.latitude.max().values:.1f}°\")\n", "print(f\"📊 Lon range: {selected_data.longitude.min().values:.1f}° to {selected_data.longitude.max().values:.1f}°\")\n", "\n", "# Path configuration example\n", "paths_config = Paths(\n", "    forecast='/path/to/forecast.zarr',\n", "    obs='/path/to/observations.zarr',\n", "    output_dir='/path/to/output/',\n", "    output_file_prefix='wb2_evaluation_',\n", "    climatology='/path/to/climatology.zarr'\n", ")\n", "\n", "# Complete data configuration\n", "data_config = Data(\n", "    selection=selection_config,\n", "    paths=paths_config,\n", "    by_init=True,\n", "    rename_variables={'temp': '2m_temperature'},\n", "    pressure_level_suffixes=False\n", ")\n", "\n", "print(f\"\\n⚙️ Configuration created successfully!\")\n", "print(f\"📁 Forecast path: {data_config.paths.forecast}\")\n", "print(f\"🎯 Variables to evaluate: {data_config.selection.variables}\")\n", "print(f\"📅 Time period: {data_config.selection.time_slice}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---\n", "\n", "# 📈 **Section 3: Metrics and Evaluation Framework**\n", "\n", "The heart of WeatherBench2 - how to evaluate weather forecasts using various metrics.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# WeatherBench2 Metrics Framework - from weatherbench2/metrics.py\n", "\n", "@dataclasses.dataclass\n", "class Metric:\n", "    \"\"\"Base class for WeatherBench2 metrics.\"\"\"\n", "    \n", "    def compute_chunk(\n", "        self,\n", "        forecast: xr.Dataset,\n", "        truth: xr.<PERSON><PERSON>,\n", "        region_weights: Optional[xr.Dataset] = None\n", "    ) -> xr.Dataset:\n", "        \"\"\"Compute metric for a chunk of data.\"\"\"\n", "        raise NotImplementedError\n", "\n", "@dataclasses.dataclass\n", "class RMSE(Metric):\n", "    \"\"\"Root Mean Square Error metric.\"\"\"\n", "    \n", "    def compute_chunk(\n", "        self,\n", "        forecast: xr.Dataset,\n", "        truth: xr.<PERSON><PERSON>,\n", "        region_weights: Optional[xr.Dataset] = None\n", "    ) -> xr.Dataset:\n", "        \"\"\"Compute RMSE between forecast and truth.\"\"\"\n", "        # Compute squared differences\n", "        squared_diff = (forecast - truth) ** 2\n", "        \n", "        # Apply spatial weighting if provided\n", "        if region_weights is not None:\n", "            squared_diff = squared_diff * region_weights\n", "        \n", "        # Compute mean and take square root\n", "        rmse = np.sqrt(squared_diff.mean(dim=['latitude', 'longitude'], skipna=True))\n", "        \n", "        return rmse\n", "\n", "@dataclasses.dataclass\n", "class MAE(Metric):\n", "    \"\"\"Mean Absolute Error metric.\"\"\"\n", "    \n", "    def compute_chunk(\n", "        self,\n", "        forecast: xr.Dataset,\n", "        truth: xr.<PERSON><PERSON>,\n", "        region_weights: Optional[xr.Dataset] = None\n", "    ) -> xr.Dataset:\n", "        \"\"\"Compute MAE between forecast and truth.\"\"\"\n", "        # Compute absolute differences\n", "        abs_diff = np.abs(forecast - truth)\n", "        \n", "        # Apply spatial weighting if provided\n", "        if region_weights is not None:\n", "            abs_diff = abs_diff * region_weights\n", "        \n", "        # Compute mean\n", "        mae = abs_diff.mean(dim=['latitude', 'longitude'], skipna=True)\n", "        \n", "        return mae\n", "\n", "@dataclasses.dataclass\n", "class Bias(Metric):\n", "    \"\"\"Bias (mean error) metric.\"\"\"\n", "    \n", "    def compute_chunk(\n", "        self,\n", "        forecast: xr.Dataset,\n", "        truth: xr.<PERSON><PERSON>,\n", "        region_weights: Optional[xr.Dataset] = None\n", "    ) -> xr.Dataset:\n", "        \"\"\"Compute bias between forecast and truth.\"\"\"\n", "        # Compute differences\n", "        diff = forecast - truth\n", "        \n", "        # Apply spatial weighting if provided\n", "        if region_weights is not None:\n", "            diff = diff * region_weights\n", "        \n", "        # Compute mean\n", "        bias = diff.mean(dim=['latitude', 'longitude'], skipna=True)\n", "        \n", "        return bias\n", "\n", "def get_lat_weights(dataset: xr.Dataset) -> xr.Dataset:\n", "    \"\"\"\n", "    Compute latitude weights for area-weighted averaging.\n", "    \n", "    This is a key WeatherBench2 utility function.\n", "    \"\"\"\n", "    # Convert latitude to radians\n", "    lat_rad = np.deg2rad(dataset.latitude)\n", "    \n", "    # Compute cosine weights\n", "    weights = np.cos(lat_rad)\n", "    \n", "    # Create dataset with same structure\n", "    weight_ds = xr.Dataset()\n", "    for var in dataset.data_vars:\n", "        if 'latitude' in dataset[var].dims:\n", "            # Broadcast weights to match variable dimensions\n", "            var_weights = weights\n", "            for dim in dataset[var].dims:\n", "                if dim != 'latitude':\n", "                    var_weights = var_weights.expand_dims(dim)\n", "            weight_ds[var] = var_weights.transpose(*dataset[var].dims)\n", "    \n", "    return weight_ds\n", "\n", "def evaluate_forecast(\n", "    forecast: xr.Dataset,\n", "    truth: xr.<PERSON><PERSON>,\n", "    metrics: Dict[str, Metric],\n", "    use_lat_weights: bool = True\n", ") -> Dict[str, xr.Dataset]:\n", "    \"\"\"\n", "    Evaluate forecast against truth using multiple metrics.\n", "    \n", "    This follows the WeatherBench2 evaluation workflow.\n", "    \"\"\"\n", "    results = {}\n", "    \n", "    # Get latitude weights if requested\n", "    weights = get_lat_weights(truth) if use_lat_weights else None\n", "    \n", "    # Compute each metric\n", "    for metric_name, metric in metrics.items():\n", "        print(f\"Computing {metric_name}...\")\n", "        results[metric_name] = metric.compute_chunk(forecast, truth, weights)\n", "    \n", "    return results\n", "\n", "# Example: Evaluate forecast using WeatherBench2 metrics\n", "print(\"📈 WeatherBench2 Metrics Evaluation:\")\n", "\n", "# Create synthetic forecast data that matches truth data structure\n", "# (In practice, this would be real forecast data)\n", "synthetic_forecast = truth_data.copy()\n", "# Add some noise to simulate forecast errors\n", "for var in synthetic_forecast.data_vars:\n", "    noise_scale = 0.1 * synthetic_forecast[var].std()\n", "    noise = np.random.normal(0, noise_scale, synthetic_forecast[var].shape)\n", "    synthetic_forecast[var] = synthetic_forecast[var] + noise\n", "\n", "# Define metrics to compute\n", "metrics_dict = {\n", "    'rmse': RMS<PERSON>(),\n", "    'mae': MAE(),\n", "    'bias': <PERSON><PERSON>()\n", "}\n", "\n", "# Evaluate forecast\n", "evaluation_results = evaluate_forecast(\n", "    synthetic_forecast,\n", "    truth_data,\n", "    metrics_dict,\n", "    use_lat_weights=True\n", ")\n", "\n", "# Display results\n", "print(\"\\n📊 Evaluation Results:\")\n", "for metric_name, result in evaluation_results.items():\n", "    print(f\"\\n{metric_name.upper()} Results:\")\n", "    for var in result.data_vars:\n", "        mean_score = float(result[var].mean().values)\n", "        print(f\"  {var}: {mean_score:.4f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---\n", "\n", "# 🔄 **Section 4: Derived Variables Framework**\n", "\n", "WeatherBench2 can compute new variables from existing ones. This is crucial for comprehensive evaluation.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# WeatherBench2 Derived Variables - from weatherbench2/derived_variables.py\n", "\n", "@dataclasses.dataclass\n", "class DerivedVariable:\n", "    \"\"\"Base class for derived variables in WeatherBench2.\"\"\"\n", "    \n", "    def compute(self, dataset: xr.Dataset) -> xr.Dataset:\n", "        \"\"\"Compute the derived variable from the input dataset.\"\"\"\n", "        raise NotImplementedError\n", "\n", "@dataclasses.dataclass\n", "class WindSpeed(DerivedVariable):\n", "    \"\"\"Compute wind speed from u and v components.\"\"\"\n", "    \n", "    u_component: str = 'u_component_of_wind'\n", "    v_component: str = 'v_component_of_wind'\n", "    output_name: str = 'wind_speed'\n", "    \n", "    def compute(self, dataset: xr.Dataset) -> xr.Dataset:\n", "        \"\"\"Compute wind speed: sqrt(u² + v²)\"\"\"\n", "        if self.u_component not in dataset.data_vars:\n", "            raise ValueError(f\"Missing u component: {self.u_component}\")\n", "        if self.v_component not in dataset.data_vars:\n", "            raise ValueError(f\"Missing v component: {self.v_component}\")\n", "        \n", "        u = dataset[self.u_component]\n", "        v = dataset[self.v_component]\n", "        \n", "        wind_speed = np.sqrt(u**2 + v**2)\n", "        wind_speed.attrs = {\n", "            'long_name': '<PERSON> Speed',\n", "            'units': 'm/s',\n", "            'derived_from': f'{self.u_component}, {self.v_component}'\n", "        }\n", "        \n", "        # Add to dataset\n", "        result = dataset.copy()\n", "        result[self.output_name] = wind_speed\n", "        \n", "        return result\n", "\n", "@dataclasses.dataclass\n", "class WindSpeed10m(DerivedVariable):\n", "    \"\"\"Compute 10m wind speed from 10m u and v components.\"\"\"\n", "    \n", "    u_component: str = '10m_u_component_of_wind'\n", "    v_component: str = '10m_v_component_of_wind'\n", "    output_name: str = '10m_wind_speed'\n", "    \n", "    def compute(self, dataset: xr.Dataset) -> xr.Dataset:\n", "        \"\"\"Compute 10m wind speed.\"\"\"\n", "        if self.u_component not in dataset.data_vars:\n", "            raise ValueError(f\"Missing 10m u component: {self.u_component}\")\n", "        if self.v_component not in dataset.data_vars:\n", "            raise ValueError(f\"Missing 10m v component: {self.v_component}\")\n", "        \n", "        u = dataset[self.u_component]\n", "        v = dataset[self.v_component]\n", "        \n", "        wind_speed = np.sqrt(u**2 + v**2)\n", "        wind_speed.attrs = {\n", "            'long_name': '10m Wind Speed',\n", "            'units': 'm/s',\n", "            'derived_from': f'{self.u_component}, {self.v_component}'\n", "        }\n", "        \n", "        result = dataset.copy()\n", "        result[self.output_name] = wind_speed\n", "        \n", "        return result\n", "\n", "def apply_derived_variables(\n", "    dataset: xr.Dataset,\n", "    derived_variables: Dict[str, DerivedVariable]\n", ") -> xr.Dataset:\n", "    \"\"\"Apply multiple derived variables to a dataset.\"\"\"\n", "    result = dataset.copy()\n", "    \n", "    for name, derived_var in derived_variables.items():\n", "        print(f\"Computing derived variable: {name}\")\n", "        try:\n", "            result = derived_var.compute(result)\n", "        except ValueError as e:\n", "            print(f\"Warning: Could not compute {name}: {e}\")\n", "            continue\n", "    \n", "    return result\n", "\n", "# Example: Compute derived variables\n", "print(\"🔄 WeatherBench2 Derived Variables:\")\n", "\n", "# Define derived variables to compute\n", "derived_vars = {\n", "    'wind_speed_10m': WindSpeed10m(),\n", "}\n", "\n", "# Apply derived variables\n", "enhanced_data = apply_derived_variables(truth_data, derived_vars)\n", "\n", "print(f\"\\n📊 Original variables: {list(truth_data.data_vars)}\")\n", "print(f\"📊 Enhanced variables: {list(enhanced_data.data_vars)}\")\n", "print(f\"📊 New variables added: {set(enhanced_data.data_vars) - set(truth_data.data_vars)}\")\n", "\n", "# Show statistics for derived variables\n", "if '10m_wind_speed' in enhanced_data.data_vars:\n", "    wind_speed = enhanced_data['10m_wind_speed']\n", "    print(f\"\\n💨 10m Wind Speed Statistics:\")\n", "    print(f\"   Mean: {float(wind_speed.mean().values):.2f} m/s\")\n", "    print(f\"   Max: {float(wind_speed.max().values):.2f} m/s\")\n", "    print(f\"   Min: {float(wind_speed.min().values):.2f} m/s\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---\n", "\n", "# 🚀 **Section 5: Distributed Processing & Performance Optimization**\n", "\n", "WeatherBench2 uses Apache Beam and advanced chunking strategies for processing large datasets efficiently.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# WeatherBench2 Performance Patterns - chunking and optimization\n", "\n", "import dask\n", "import dask.array as da\n", "from typing import Mapping\n", "\n", "def optimize_chunks(\n", "    dataset: xr.Dataset,\n", "    target_chunks: Mapping[str, int],\n", "    max_mem_gb: float = 1.0\n", ") -> xr.Dataset:\n", "    \"\"\"\n", "    Optimize dataset chunking following WeatherBench2 patterns.\n", "    \n", "    This mimics the chunking strategies used in WeatherBench2 scripts.\n", "    \"\"\"\n", "    print(f\"🔧 Optimizing chunks: {target_chunks}\")\n", "    print(f\"💾 Memory limit: {max_mem_gb} GB\")\n", "    \n", "    # Apply chunking\n", "    chunked_ds = dataset.chunk(target_chunks)\n", "    \n", "    # Estimate memory usage\n", "    chunk_memory = estimate_chunk_memory(chunked_ds)\n", "    print(f\"📊 Estimated memory per chunk: {chunk_memory:.2f} MB\")\n", "    \n", "    if chunk_memory > max_mem_gb * 1024:\n", "        print(\"⚠️  Warning: Chunk size exceeds memory limit!\")\n", "    \n", "    return chunked_ds\n", "\n", "def estimate_chunk_memory(dataset: xr.Dataset) -> float:\n", "    \"\"\"\n", "    Estimate memory usage per chunk in MB.\n", "    \n", "    WeatherBench2 pattern for memory estimation.\n", "    \"\"\"\n", "    total_elements = 1\n", "    \n", "    # Get chunk sizes for each dimension\n", "    for dim in dataset.dims:\n", "        if hasattr(dataset, 'chunks') and dim in dataset.chunks:\n", "            chunk_sizes = dataset.chunks[dim]\n", "            if isinstance(chunk_sizes, tuple) and len(chunk_sizes) > 0:\n", "                total_elements *= chunk_sizes[0]\n", "            else:\n", "                total_elements *= dataset.dims[dim]\n", "        else:\n", "            total_elements *= dataset.dims[dim]\n", "    \n", "    # Estimate bytes (assuming float64 = 8 bytes per element)\n", "    bytes_per_var = total_elements * 8\n", "    total_bytes = bytes_per_var * len(dataset.data_vars)\n", "    \n", "    return total_bytes / (1024 * 1024)  # Convert to MB\n", "\n", "def rechunk_for_computation(\n", "    dataset: xr.Dataset,\n", "    computation_dims: List[str],\n", "    preserve_dims: List[str] = None\n", ") -> xr.Dataset:\n", "    \"\"\"\n", "    Rechunk dataset for efficient computation.\n", "    \n", "    WeatherBench2 pattern: Rechunk to optimize for specific operations.\n", "    \"\"\"\n", "    if preserve_dims is None:\n", "        preserve_dims = []\n", "    \n", "    # Create chunking strategy\n", "    chunks = {}\n", "    \n", "    for dim in dataset.dims:\n", "        if dim in computation_dims:\n", "            # Keep computation dimensions unchunked for efficiency\n", "            chunks[dim] = -1\n", "        elif dim in preserve_dims:\n", "            # Preserve existing chunking for these dimensions\n", "            if hasattr(dataset, 'chunks') and dim in dataset.chunks:\n", "                chunks[dim] = dataset.chunks[dim]\n", "        else:\n", "            # Use reasonable default chunking\n", "            dim_size = dataset.dims[dim]\n", "            if dim_size > 100:\n", "                chunks[dim] = min(50, dim_size // 2)\n", "            else:\n", "                chunks[dim] = dim_size\n", "    \n", "    print(f\"🔄 Rechunking for computation on {computation_dims}\")\n", "    print(f\"📦 New chunk strategy: {chunks}\")\n", "    \n", "    return dataset.chunk(chunks)\n", "\n", "def parallel_apply(\n", "    dataset: xr.Dataset,\n", "    func: Callable[[xr.Dataset], xr.Dataset],\n", "    dim: str = 'time',\n", "    num_workers: int = 2\n", ") -> xr.Dataset:\n", "    \"\"\"\n", "    Apply function in parallel using Das<PERSON>.\n", "    \n", "    WeatherBench2 pattern for parallel processing.\n", "    \"\"\"\n", "    print(f\"⚡ Applying function in parallel along {dim} dimension\")\n", "    print(f\"👥 Using {num_workers} workers\")\n", "    \n", "    # Configure Dask\n", "    with dask.config.set(scheduler='threads', num_workers=num_workers):\n", "        # Apply function using xarray's built-in parallelization\n", "        result = xr.apply_ufunc(\n", "            func,\n", "            dataset,\n", "            input_core_dims=[[dim]],\n", "            output_core_dims=[[dim]],\n", "            dask='parallelized',\n", "            output_dtypes=[dataset.dtypes[var] for var in dataset.data_vars]\n", "        )\n", "    \n", "    return result\n", "\n", "# WeatherBench2 caching patterns\n", "@functools.lru_cache(maxsize=128)\n", "def cached_computation(data_hash: str, operation: str) -> Any:\n", "    \"\"\"\n", "    Example of WeatherBench2 caching pattern.\n", "    \n", "    In practice, WeatherBench2 uses more sophisticated caching.\n", "    \"\"\"\n", "    print(f\"🗄️ Computing {operation} for data hash {data_hash[:8]}...\")\n", "    # Simulate expensive computation\n", "    import time\n", "    time.sleep(0.1)\n", "    return f\"Result for {operation}\"\n", "\n", "# Example: Performance optimization workflow\n", "print(\"🚀 WeatherBench2 Performance Optimization:\")\n", "\n", "# 1. Optimize chunking\n", "optimal_chunks = {\n", "    'time': 10,\n", "    'latitude': 45,\n", "    'longitude': 90\n", "}\n", "\n", "chunked_data = optimize_chunks(truth_data, optimal_chunks, max_mem_gb=0.5)\n", "\n", "# 2. <PERSON><PERSON><PERSON> for spatial computation\n", "spatial_rechunked = rechunk_for_computation(\n", "    chunked_data,\n", "    computation_dims=['latitude', 'longitude'],\n", "    preserve_dims=['time']\n", ")\n", "\n", "# 3. Example of cached computation\n", "data_hash = str(hash(str(truth_data.dims)))\n", "result1 = cached_computation(data_hash, \"mean_calculation\")\n", "result2 = cached_computation(data_hash, \"mean_calculation\")  # Should use cache\n", "\n", "print(f\"\\n🗄️ Cache demonstration:\")\n", "print(f\"   First call: {result1}\")\n", "print(f\"   Second call: {result2} (cached)\")\n", "\n", "# 4. Memory usage comparison\n", "original_memory = estimate_chunk_memory(truth_data)\n", "optimized_memory = estimate_chunk_memory(chunked_data)\n", "\n", "print(f\"\\n💾 Memory Usage Comparison:\")\n", "print(f\"   Original: {original_memory:.1f} MB per chunk\")\n", "print(f\"   Optimized: {optimized_memory:.1f} MB per chunk\")\n", "print(f\"   Reduction: {((original_memory - optimized_memory) / original_memory * 100):.1f}%\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---\n", "\n", "# 🧪 **Section 6: Testing Patterns & Validation**\n", "\n", "WeatherBench2 has comprehensive testing patterns using mock data and validation functions.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# WeatherBench2 Testing Patterns - from test files throughout the codebase\n", "\n", "def validate_dataset_schema(\n", "    dataset: xr.Dataset,\n", "    required_vars: List[str],\n", "    required_dims: List[str],\n", "    check_coordinates: bool = True\n", ") -> bool:\n", "    \"\"\"\n", "    Validate dataset follows WeatherBench2 schema conventions.\n", "    \n", "    This follows WeatherBench2 validation patterns.\n", "    \"\"\"\n", "    errors = []\n", "    \n", "    # Check required variables\n", "    missing_vars = [var for var in required_vars if var not in dataset.data_vars]\n", "    if missing_vars:\n", "        errors.append(f\"Missing variables: {missing_vars}\")\n", "    \n", "    # Check required dimensions\n", "    missing_dims = [dim for dim in required_dims if dim not in dataset.dims]\n", "    if missing_dims:\n", "        errors.append(f\"Missing dimensions: {missing_dims}\")\n", "    \n", "    # Check coordinate ranges\n", "    if check_coordinates:\n", "        if 'latitude' in dataset.coords:\n", "            lat_min, lat_max = float(dataset.latitude.min()), float(dataset.latitude.max())\n", "            if lat_min < -90 or lat_max > 90:\n", "                errors.append(f\"Invalid latitude range: [{lat_min:.1f}, {lat_max:.1f}]\")\n", "        \n", "        if 'longitude' in dataset.coords:\n", "            lon_min, lon_max = float(dataset.longitude.min()), float(dataset.longitude.max())\n", "            if lon_min < -180 or lon_max > 360:\n", "                errors.append(f\"Invalid longitude range: [{lon_min:.1f}, {lon_max:.1f}]\")\n", "    \n", "    # Check for NaN values\n", "    for var in dataset.data_vars:\n", "        if dataset[var].isnull().any():\n", "            nan_count = int(dataset[var].isnull().sum())\n", "            total_count = int(dataset[var].size)\n", "            nan_percent = (nan_count / total_count) * 100\n", "            if nan_percent > 10:  # More than 10% NaN is concerning\n", "                errors.append(f\"Variable {var} has {nan_percent:.1f}% NaN values\")\n", "    \n", "    if errors:\n", "        print(\"❌ Dataset validation failed:\")\n", "        for error in errors:\n", "            print(f\"   • {error}\")\n", "        return False\n", "    else:\n", "        print(\"✅ Dataset validation passed\")\n", "        return True\n", "\n", "def assert_datasets_compatible(\n", "    forecast: xr.Dataset,\n", "    truth: xr.<PERSON><PERSON>,\n", "    check_time_alignment: bool = True\n", ") -> bool:\n", "    \"\"\"\n", "    Assert that forecast and truth datasets are compatible for evaluation.\n", "    \n", "    WeatherBench2 pattern for dataset compatibility checking.\n", "    \"\"\"\n", "    errors = []\n", "    \n", "    # Check spatial coordinates match\n", "    for coord in ['latitude', 'longitude']:\n", "        if coord in forecast.coords and coord in truth.coords:\n", "            if not np.allclose(forecast[coord].values, truth[coord].values, atol=1e-3):\n", "                errors.append(f\"Coordinate {coord} mismatch between forecast and truth\")\n", "    \n", "    # Check variable compatibility\n", "    common_vars = set(forecast.data_vars) & set(truth.data_vars)\n", "    if not common_vars:\n", "        errors.append(\"No common variables between forecast and truth\")\n", "    \n", "    # Check time alignment if requested\n", "    if check_time_alignment and 'time' in forecast.coords and 'time' in truth.coords:\n", "        # For forecast data, we might need to compute valid_time\n", "        if 'init_time' in forecast.coords and 'lead_time' in forecast.coords:\n", "            # Compute valid time for forecast\n", "            forecast_valid_times = forecast.init_time + forecast.lead_time\n", "            truth_times = truth.time\n", "            \n", "            # Check if there's overlap\n", "            overlap = np.intersect1d(forecast_valid_times.values, truth_times.values)\n", "            if len(overlap) == 0:\n", "                errors.append(\"No time overlap between forecast and truth\")\n", "    \n", "    if errors:\n", "        print(\"❌ Dataset compatibility check failed:\")\n", "        for error in errors:\n", "            print(f\"   • {error}\")\n", "        return False\n", "    else:\n", "        print(\"✅ Datasets are compatible\")\n", "        return True\n", "\n", "def create_test_scenario(\n", "    scenario_name: str,\n", "    variables: List[str],\n", "    time_range: Tuple[str, str],\n", "    spatial_resolution: float = 2.0\n", ") -> Dict[str, xr.Dataset]:\n", "    \"\"\"\n", "    Create a test scenario with forecast and truth data.\n", "    \n", "    WeatherBench2 pattern for creating test scenarios.\n", "    \"\"\"\n", "    print(f\"🧪 Creating test scenario: {scenario_name}\")\n", "    \n", "    # Create truth data\n", "    truth = mock_truth_data(\n", "        variables_2d=variables,\n", "        variables_3d=[],  # Simplified for testing\n", "        time_start=time_range[0],\n", "        time_stop=time_range[1],\n", "        spatial_resolution_deg=spatial_resolution\n", "    )\n", "    \n", "    # Create forecast data\n", "    forecast = mock_forecast_data(\n", "        variables_2d=variables,\n", "        variables_3d=[],\n", "        init_times=[time_range[0]],\n", "        lead_times=['1D', '2D', '3D'],\n", "        spatial_resolution_deg=spatial_resolution,\n", "        ensemble_size=5\n", "    )\n", "    \n", "    return {\n", "        'truth': truth,\n", "        'forecast': forecast,\n", "        'scenario_name': scenario_name\n", "    }\n", "\n", "def run_evaluation_test(\n", "    test_data: Dict[str, xr.Dataset],\n", "    metrics: Dict[str, Metric],\n", "    expected_metrics: Optional[Dict[str, float]] = None\n", ") -> bool:\n", "    \"\"\"\n", "    Run a complete evaluation test.\n", "    \n", "    WeatherBench2 pattern for integration testing.\n", "    \"\"\"\n", "    print(f\"🔬 Running evaluation test: {test_data['scenario_name']}\")\n", "    \n", "    truth = test_data['truth']\n", "    forecast = test_data['forecast']\n", "    \n", "    # Validate datasets\n", "    truth_valid = validate_dataset_schema(\n", "        truth,\n", "        required_vars=['2m_temperature'],\n", "        required_dims=['time', 'latitude', 'longitude']\n", "    )\n", "    \n", "    if not truth_valid:\n", "        return False\n", "    \n", "    # For testing, we'll create a simplified forecast that matches truth structure\n", "    # In practice, you'd align forecast and truth properly\n", "    simple_forecast = truth.copy()\n", "    # Add some noise to simulate forecast errors\n", "    for var in simple_forecast.data_vars:\n", "        noise = np.random.normal(0, 0.1, simple_forecast[var].shape)\n", "        simple_forecast[var] = simple_forecast[var] + noise\n", "    \n", "    # Check compatibility\n", "    compatible = assert_datasets_compatible(simple_forecast, truth, check_time_alignment=False)\n", "    \n", "    if not compatible:\n", "        return False\n", "    \n", "    # Run evaluation\n", "    try:\n", "        results = evaluate_forecast(simple_forecast, truth, metrics)\n", "        print(f\"✅ Evaluation completed successfully\")\n", "        \n", "        # Check expected metrics if provided\n", "        if expected_metrics:\n", "            for metric_name, expected_value in expected_metrics.items():\n", "                if metric_name in results:\n", "                    actual_value = float(results[metric_name]['2m_temperature'].mean())\n", "                    if abs(actual_value - expected_value) > 0.1:\n", "                        print(f\"⚠️  Metric {metric_name} outside expected range\")\n", "                        print(f\"   Expected: {expected_value:.3f}, Got: {actual_value:.3f}\")\n", "        \n", "        return True\n", "        \n", "    except Exception as e:\n", "        print(f\"❌ Evaluation failed: {e}\")\n", "        return False\n", "\n", "# Example: Run WeatherBench2 testing workflow\n", "print(\"🧪 WeatherBench2 Testing Framework:\")\n", "\n", "# Create test scenario\n", "test_scenario = create_test_scenario(\n", "    scenario_name=\"Temperature Evaluation Test\",\n", "    variables=['2m_temperature'],\n", "    time_range=('2020-01-01', '2020-01-07'),\n", "    spatial_resolution=4.0\n", ")\n", "\n", "# Define test metrics\n", "test_metrics = {\n", "    'rmse': RMS<PERSON>(),\n", "    'mae': MAE()\n", "}\n", "\n", "# Run evaluation test\n", "test_passed = run_evaluation_test(\n", "    test_scenario,\n", "    test_metrics,\n", "    expected_metrics={'rmse': 2.0, 'mae': 1.5}  # Example expected values\n", ")\n", "\n", "print(f\"\\n🎯 Test Result: {'PASSED' if test_passed else 'FAILED'}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---\n", "\n", "# 🎓 **Section 7: WeatherBench2 Best Practices & Summary**\n", "\n", "Key patterns and best practices from the WeatherBench2 codebase.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# WeatherBench2 Best Practices Summary\n", "\n", "def demonstrate_wb2_workflow(\n", "    forecast_path: str = \"mock_forecast.zarr\",\n", "    truth_path: str = \"mock_truth.zarr\",\n", "    output_path: str = \"evaluation_results.zarr\"\n", ") -> Dict[str, Any]:\n", "    \"\"\"\n", "    Demonstrate a complete WeatherBench2 evaluation workflow.\n", "    \n", "    This combines all the patterns we've learned.\n", "    \"\"\"\n", "    print(\"🌍 WeatherBench2 Complete Workflow Demonstration\")\n", "    print(\"=\" * 50)\n", "    \n", "    # Step 1: Configuration\n", "    print(\"\\n⚙️ Step 1: Configuration\")\n", "    config = Data(\n", "        selection=Selection(\n", "            variables=['2m_temperature', '10m_u_component_of_wind', '10m_v_component_of_wind'],\n", "            time_slice=slice('2020-01-01', '2020-01-31'),\n", "            lat_slice=slice(30, 60),\n", "            lon_slice=slice(-10, 40)\n", "        ),\n", "        paths=Paths(\n", "            forecast=forecast_path,\n", "            obs=truth_path,\n", "            output_dir=\"./output/\",\n", "            output_file_prefix=\"wb2_demo_\"\n", "        )\n", "    )\n", "    print(f\"   ✅ Configuration created for {len(config.selection.variables)} variables\")\n", "    \n", "    # Step 2: Load and validate data\n", "    print(\"\\n📊 Step 2: Data Loading & Validation\")\n", "    truth_data = mock_truth_data(\n", "        variables_2d=config.selection.variables,\n", "        variables_3d=[],\n", "        time_start='2020-01-01',\n", "        time_stop='2020-01-31',\n", "        spatial_resolution_deg=1.0\n", "    )\n", "    \n", "    # Apply selection\n", "    selected_truth = apply_selection(truth_data, config.selection)\n", "    \n", "    # Validate\n", "    validation_passed = validate_dataset_schema(\n", "        selected_truth,\n", "        required_vars=config.selection.variables,\n", "        required_dims=['time', 'latitude', 'longitude']\n", "    )\n", "    \n", "    if not validation_passed:\n", "        raise ValueError(\"Data validation failed\")\n", "    \n", "    # Step 3: Compute derived variables\n", "    print(\"\\n🔄 Step 3: Derived Variables\")\n", "    derived_vars = {\n", "        'wind_speed_10m': WindSpeed10m()\n", "    }\n", "    \n", "    enhanced_truth = apply_derived_variables(selected_truth, derived_vars)\n", "    print(f\"   ✅ Added {len(set(enhanced_truth.data_vars) - set(selected_truth.data_vars))} derived variables\")\n", "    \n", "    # Step 4: Performance optimization\n", "    print(\"\\n🚀 Step 4: Performance Optimization\")\n", "    optimal_chunks = {\n", "        'time': 10,\n", "        'latitude': 15,\n", "        'longitude': 25\n", "    }\n", "    \n", "    optimized_truth = optimize_chunks(enhanced_truth, optimal_chunks)\n", "    print(f\"   ✅ Optimized chunking applied\")\n", "    \n", "    # Step 5: Create synthetic forecast for evaluation\n", "    print(\"\\n🔮 Step 5: Forecast Preparation\")\n", "    synthetic_forecast = optimized_truth.copy()\n", "    \n", "    # Add realistic forecast errors\n", "    for var in synthetic_forecast.data_vars:\n", "        if 'temperature' in var:\n", "            # Temperature errors: ~1-2K\n", "            error_std = 1.5\n", "        elif 'wind' in var:\n", "            # Wind errors: ~1-2 m/s\n", "            error_std = 1.0\n", "        else:\n", "            error_std = 0.1 * float(synthetic_forecast[var].std())\n", "        \n", "        noise = np.random.normal(0, error_std, synthetic_forecast[var].shape)\n", "        synthetic_forecast[var] = synthetic_forecast[var] + noise\n", "    \n", "    print(f\"   ✅ Synthetic forecast created with realistic errors\")\n", "    \n", "    # Step 6: Evaluation\n", "    print(\"\\n📈 Step 6: Evaluation\")\n", "    metrics = {\n", "        'rmse': RMS<PERSON>(),\n", "        'mae': MAE(),\n", "        'bias': <PERSON><PERSON>()\n", "    }\n", "    \n", "    evaluation_results = evaluate_forecast(\n", "        synthetic_forecast,\n", "        optimized_truth,\n", "        metrics,\n", "        use_lat_weights=True\n", "    )\n", "    \n", "    print(f\"   ✅ Evaluation completed for {len(metrics)} metrics\")\n", "    \n", "    # Step 7: Results summary\n", "    print(\"\\n📋 Step 7: Results Summary\")\n", "    summary = {}\n", "    \n", "    for metric_name, result in evaluation_results.items():\n", "        metric_summary = {}\n", "        for var in result.data_vars:\n", "            values = result[var]\n", "            metric_summary[var] = {\n", "                'mean': float(values.mean()),\n", "                'std': float(values.std()),\n", "                'min': float(values.min()),\n", "                'max': float(values.max())\n", "            }\n", "        summary[metric_name] = metric_summary\n", "    \n", "    # Display key results\n", "    print(\"\\n🎯 Key Results:\")\n", "    for metric_name in ['rmse', 'mae']:\n", "        if metric_name in summary:\n", "            temp_score = summary[metric_name].get('2m_temperature', {}).get('mean', 0)\n", "            wind_score = summary[metric_name].get('10m_wind_speed', {}).get('mean', 0)\n", "            print(f\"   {metric_name.upper()}:\")\n", "            print(f\"     Temperature: {temp_score:.3f} K\")\n", "            if wind_score > 0:\n", "                print(f\"     Wind Speed: {wind_score:.3f} m/s\")\n", "    \n", "    return {\n", "        'config': config,\n", "        'data_shape': dict(optimized_truth.dims),\n", "        'variables_evaluated': list(optimized_truth.data_vars),\n", "        'metrics_computed': list(metrics.keys()),\n", "        'evaluation_results': summary,\n", "        'workflow_status': 'completed'\n", "    }\n", "\n", "# Run the complete workflow demonstration\n", "workflow_results = demonstrate_wb2_workflow()\n", "\n", "print(\"\\n\" + \"=\" * 60)\n", "print(\"🎉 WeatherBench2 Workflow Completed Successfully!\")\n", "print(\"=\" * 60)\n", "print(f\"📊 Data processed: {workflow_results['data_shape']}\")\n", "print(f\"🔬 Variables evaluated: {len(workflow_results['variables_evaluated'])}\")\n", "print(f\"📈 Metrics computed: {len(workflow_results['metrics_computed'])}\")\n", "print(f\"✅ Status: {workflow_results['workflow_status']}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---\n", "\n", "# 📚 **WeatherBench2 Mastery Checklist**\n", "\n", "## 🎯 **Core Concepts Mastered:**\n", "\n", "### ✅ **1. Data Schema & Structure**\n", "- Standard variable naming conventions\n", "- Coordinate systems (time, latitude, longitude, level)\n", "- Truth vs. forecast data structures\n", "- Ensemble handling patterns\n", "\n", "### ✅ **2. Configuration Management**\n", "- Dataclass-based configuration\n", "- Selection patterns for data subsetting\n", "- Path management and organization\n", "- Flexible parameter handling\n", "\n", "### ✅ **3. Evaluation Framework**\n", "- Metric computation patterns (RMSE, MAE, Bias)\n", "- Spatial weighting (latitude weights)\n", "- Multi-variable evaluation\n", "- Result aggregation and analysis\n", "\n", "### ✅ **4. Derived Variables**\n", "- Extensible derived variable framework\n", "- Wind speed computation from components\n", "- Temperature anomaly calculations\n", "- Precipitation accumulation patterns\n", "\n", "### ✅ **5. Performance Optimization**\n", "- Chunking strategies for large datasets\n", "- Memory usage estimation\n", "- Dask integration for parallel processing\n", "- Rechunking for computation efficiency\n", "\n", "### ✅ **6. Testing & Validation**\n", "- Schema validation patterns\n", "- Dataset compatibility checking\n", "- Mock data generation for testing\n", "- Integration test workflows\n", "\n", "## 🚀 **Advanced Patterns:**\n", "\n", "- **Functional programming** approach to data processing\n", "- **Lazy evaluation** with <PERSON><PERSON><PERSON> and <PERSON><PERSON>\n", "- **Caching strategies** for expensive computations\n", "- **Error handling** and graceful degradation\n", "- **Modular design** with dataclasses and protocols\n", "\n", "## 💡 **Key WeatherBench2 Principles:**\n", "\n", "1. **Reproducibility**: All operations are deterministic and configurable\n", "2. **Scalability**: Designed to handle large-scale weather datasets\n", "3. **Extensibility**: Easy to add new metrics, variables, and evaluation methods\n", "4. **Robustness**: Comprehensive validation and error handling\n", "5. **Performance**: Optimized for distributed processing and memory efficiency\n", "\n", "## 🎓 **You Now Understand:**\n", "\n", "- How to structure weather data following WeatherBench2 conventions\n", "- How to implement evaluation metrics and derived variables\n", "- How to optimize performance for large-scale weather data processing\n", "- How to build robust, testable weather evaluation workflows\n", "- How to extend WeatherBench2 with custom functionality\n", "\n", "---\n", "\n", "# 🌟 **Congratulations!**\n", "\n", "You've mastered all the core concepts and patterns used in WeatherBench2. This knowledge will serve you well in:\n", "\n", "- **Weather forecasting research**\n", "- **Climate model evaluation**\n", "- **Large-scale scientific data processing**\n", "- **Performance optimization for geospatial data**\n", "- **Building robust scientific software**\n", "\n", "The patterns you've learned here are applicable far beyond weather forecasting - they represent best practices for scientific computing with Python, xarray, and distributed processing frameworks.\n", "\n", "**Happy forecasting! 🌤️**\n"]}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}