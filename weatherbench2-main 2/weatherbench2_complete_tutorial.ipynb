# Essential WeatherBench2 imports - memorize these patterns
import dataclasses
import typing as t
from typing import Optional, Dict, List, Tuple, Sequence, Union, Any, Callable

import numpy as np
import pandas as pd
import xarray as xr
import matplotlib.pyplot as plt
import seaborn as sns

# Scientific computing
from datetime import datetime, timedelta
import functools
import warnings
warnings.filterwarnings('ignore')

# Install required packages
!pip install xarray netcdf4 dask matplotlib seaborn cartopy -q

print("✅ WeatherBench2 environment ready!")
print("📚 This tutorial covers ALL WeatherBench2 concepts from the actual codebase")

# WeatherBench2 Schema Patterns - from weatherbench2/schema.py

# Standard variable names used throughout WeatherBench2
ALL_3D_VARIABLES = (
    'geopotential',
    'temperature', 
    'u_component_of_wind',
    'v_component_of_wind',
    'specific_humidity',
)

ALL_2D_VARIABLES = (
    '2m_temperature',
    '10m_u_component_of_wind',
    '10m_v_component_of_wind', 
    'mean_sea_level_pressure',
    'total_precipitation_6hr',
)

# Standard pressure levels (hPa)
PRESSURE_LEVELS = [50, 100, 150, 200, 250, 300, 400, 500, 600, 700, 850, 925, 1000]

def mock_truth_data(
    variables_3d: Sequence[str] = ALL_3D_VARIABLES,
    variables_2d: Sequence[str] = ALL_2D_VARIABLES,
    time_start: str = '2020-01-01',
    time_stop: str = '2020-12-31',
    spatial_resolution_deg: float = 1.0,
    levels: Sequence[int] = PRESSURE_LEVELS
) -> xr.Dataset:
    """
    Create mock truth data following WeatherBench2 schema conventions.
    
    This mimics the schema.mock_truth_data function from WeatherBench2.
    """
    # Time coordinate - daily data
    time = pd.date_range(time_start, time_stop, freq='D')
    
    # Spatial coordinates (global coverage)
    latitude = np.arange(-90, 90 + spatial_resolution_deg, spatial_resolution_deg)
    longitude = np.arange(0, 360, spatial_resolution_deg)
    
    # Pressure levels
    level = np.array(levels)
    
    # Initialize dataset
    data_vars = {}
    
    # Add 3D variables (time, level, lat, lon)
    for var in variables_3d:
        shape = (len(time), len(level), len(latitude), len(longitude))
        data = np.random.randn(*shape)
        
        # Add realistic scaling based on variable type
        if 'temperature' in var:
            data = data * 10 + 273.15  # Kelvin
        elif 'wind' in var:
            data = data * 5  # m/s
        elif 'geopotential' in var:
            data = data * 1000 + 50000  # m²/s²
        elif 'humidity' in var:
            data = np.abs(data) * 0.01  # kg/kg
            
        data_vars[var] = (['time', 'level', 'latitude', 'longitude'], data)
    
    # Add 2D variables (time, lat, lon)
    for var in variables_2d:
        shape = (len(time), len(latitude), len(longitude))
        data = np.random.randn(*shape)
        
        # Add realistic scaling
        if '2m_temperature' in var:
            data = data * 15 + 288.15  # Kelvin
        elif 'wind' in var:
            data = data * 3  # m/s
        elif 'pressure' in var:
            data = data * 1000 + 101325  # Pa
        elif 'precipitation' in var:
            data = np.abs(data) * 0.001  # m
            
        data_vars[var] = (['time', 'latitude', 'longitude'], data)
    
    # Create dataset
    ds = xr.Dataset(
        data_vars,
        coords={
            'time': time,
            'level': level,
            'latitude': latitude,
            'longitude': longitude,
        },
        attrs={
            'title': 'WeatherBench2 Mock Truth Data',
            'source': 'Synthetic data following WB2 schema',
            'spatial_resolution': f'{spatial_resolution_deg}°',
            'temporal_resolution': 'daily'
        }
    )
    
    return ds

def mock_forecast_data(
    variables_3d: Sequence[str] = ALL_3D_VARIABLES,
    variables_2d: Sequence[str] = ALL_2D_VARIABLES,
    init_times: Sequence[str] = ['2020-01-01', '2020-01-02'],
    lead_times: Sequence[str] = ['1D', '2D', '3D', '5D', '7D'],
    spatial_resolution_deg: float = 1.0,
    levels: Sequence[int] = PRESSURE_LEVELS,
    ensemble_size: int = 10
) -> xr.Dataset:
    """
    Create mock forecast data with ensemble members.
    
    This follows WeatherBench2 forecast data conventions.
    """
    # Convert lead times to timedeltas
    lead_time_deltas = [pd.Timedelta(lt) for lt in lead_times]
    
    # Initialize coordinates
    init_time = pd.to_datetime(init_times)
    lead_time = pd.to_timedelta(lead_times)
    latitude = np.arange(-90, 90 + spatial_resolution_deg, spatial_resolution_deg)
    longitude = np.arange(0, 360, spatial_resolution_deg)
    level = np.array(levels)
    realization = np.arange(ensemble_size)
    
    # Create forecast dataset similar to truth data but with additional dimensions
    data_vars = {}
    
    # Add 3D variables (init_time, lead_time, realization, level, lat, lon)
    for var in variables_3d:
        shape = (len(init_time), len(lead_time), len(realization), len(level), len(latitude), len(longitude))
        data = np.random.randn(*shape)
        
        # Apply same scaling as truth data
        if 'temperature' in var:
            data = data * 10 + 273.15
        elif 'wind' in var:
            data = data * 5
        elif 'geopotential' in var:
            data = data * 1000 + 50000
        elif 'humidity' in var:
            data = np.abs(data) * 0.01
            
        data_vars[var] = (['init_time', 'lead_time', 'realization', 'level', 'latitude', 'longitude'], data)
    
    # Add 2D variables
    for var in variables_2d:
        shape = (len(init_time), len(lead_time), len(realization), len(latitude), len(longitude))
        data = np.random.randn(*shape)
        
        if '2m_temperature' in var:
            data = data * 15 + 288.15
        elif 'wind' in var:
            data = data * 3
        elif 'pressure' in var:
            data = data * 1000 + 101325
        elif 'precipitation' in var:
            data = np.abs(data) * 0.001
            
        data_vars[var] = (['init_time', 'lead_time', 'realization', 'latitude', 'longitude'], data)
    
    # Create forecast dataset
    ds = xr.Dataset(
        data_vars,
        coords={
            'init_time': init_time,
            'lead_time': lead_time,
            'realization': realization,
            'level': level,
            'latitude': latitude,
            'longitude': longitude,
        },
        attrs={
            'title': 'WeatherBench2 Mock Forecast Data',
            'source': 'Synthetic ensemble forecast data',
            'ensemble_size': ensemble_size,
            'spatial_resolution': f'{spatial_resolution_deg}°'
        }
    )
    
    return ds

# Create sample datasets
print("🌍 Creating WeatherBench2 Schema Examples...")
truth_data = mock_truth_data(time_start='2020-01-01', time_stop='2020-01-31', spatial_resolution_deg=2.0)
forecast_data = mock_forecast_data(init_times=['2020-01-01', '2020-01-02'], spatial_resolution_deg=2.0)

print("\n📊 Truth Data Schema:")
print(truth_data)
print("\n🔮 Forecast Data Schema:")
print(forecast_data)
print(f"\n📏 Truth data size: {truth_data.nbytes / 1e6:.1f} MB")
print(f"📏 Forecast data size: {forecast_data.nbytes / 1e6:.1f} MB")

# WeatherBench2 Configuration Patterns - from weatherbench2/config.py

@dataclasses.dataclass
class Selection:
    """Select a sub-set of forecast and truth data.
    
    This is the core data selection pattern used throughout WeatherBench2.
    """
    variables: t.Sequence[str]
    time_slice: slice
    levels: t.Optional[t.Sequence[int]] = None
    lat_slice: t.Optional[slice] = dataclasses.field(
        default_factory=lambda: slice(None, None)
    )
    lon_slice: t.Optional[slice] = dataclasses.field(
        default_factory=lambda: slice(None, None)
    )
    aux_variables: t.Optional[t.Sequence[str]] = None

@dataclasses.dataclass
class Paths:
    """Configuration for input and output paths."""
    forecast: str
    obs: str
    output_dir: str
    output_file_prefix: t.Optional[str] = ''
    climatology: t.Optional[str] = None

@dataclasses.dataclass
class Data:
    """Data configuration class combining Selection and Paths."""
    selection: Selection
    paths: Paths
    by_init: t.Optional[bool] = True
    rename_variables: t.Optional[t.Dict[str, str]] = None
    pressure_level_suffixes: t.Optional[bool] = False

def apply_selection(dataset: xr.Dataset, selection: Selection) -> xr.Dataset:
    """
    Apply data selection following WeatherBench2 patterns.
    
    This is how WeatherBench2 handles data subsetting throughout the codebase.
    """
    # Variable selection
    if selection.variables:
        dataset = dataset[selection.variables]
    
    # Time selection
    if selection.time_slice:
        dataset = dataset.sel(time=selection.time_slice)
    
    # Level selection (for 3D variables)
    if selection.levels and 'level' in dataset.dims:
        dataset = dataset.sel(level=selection.levels)
    
    # Spatial selection
    if selection.lat_slice and selection.lat_slice != slice(None, None):
        dataset = dataset.sel(latitude=selection.lat_slice)
    if selection.lon_slice and selection.lon_slice != slice(None, None):
        dataset = dataset.sel(longitude=selection.lon_slice)
    
    return dataset

# Example usage of WeatherBench2 configuration patterns
print("⚙️ WeatherBench2 Configuration Examples:")

# Create a selection configuration
selection_config = Selection(
    variables=['2m_temperature', 'total_precipitation_6hr'],
    time_slice=slice('2020-01-01', '2020-01-15'),
    lat_slice=slice(30, 60),  # Northern mid-latitudes
    lon_slice=slice(0, 30)    # Europe region
)

# Apply selection to truth data
selected_data = apply_selection(truth_data, selection_config)

print(f"\n📊 Original data shape: {dict(truth_data.dims)}")
print(f"📊 Selected data shape: {dict(selected_data.dims)}")
print(f"📊 Selected variables: {list(selected_data.data_vars)}")
print(f"📊 Time range: {selected_data.time.min().values} to {selected_data.time.max().values}")
print(f"📊 Lat range: {selected_data.latitude.min().values:.1f}° to {selected_data.latitude.max().values:.1f}°")
print(f"📊 Lon range: {selected_data.longitude.min().values:.1f}° to {selected_data.longitude.max().values:.1f}°")

# Path configuration example
paths_config = Paths(
    forecast='/path/to/forecast.zarr',
    obs='/path/to/observations.zarr',
    output_dir='/path/to/output/',
    output_file_prefix='wb2_evaluation_',
    climatology='/path/to/climatology.zarr'
)

# Complete data configuration
data_config = Data(
    selection=selection_config,
    paths=paths_config,
    by_init=True,
    rename_variables={'temp': '2m_temperature'},
    pressure_level_suffixes=False
)

print(f"\n⚙️ Configuration created successfully!")
print(f"📁 Forecast path: {data_config.paths.forecast}")
print(f"🎯 Variables to evaluate: {data_config.selection.variables}")
print(f"📅 Time period: {data_config.selection.time_slice}")

# WeatherBench2 Metrics Framework - from weatherbench2/metrics.py

@dataclasses.dataclass
class Metric:
    """Base class for WeatherBench2 metrics."""
    
    def compute_chunk(
        self,
        forecast: xr.Dataset,
        truth: xr.Dataset,
        region_weights: Optional[xr.Dataset] = None
    ) -> xr.Dataset:
        """Compute metric for a chunk of data."""
        raise NotImplementedError

@dataclasses.dataclass
class RMSE(Metric):
    """Root Mean Square Error metric."""
    
    def compute_chunk(
        self,
        forecast: xr.Dataset,
        truth: xr.Dataset,
        region_weights: Optional[xr.Dataset] = None
    ) -> xr.Dataset:
        """Compute RMSE between forecast and truth."""
        # Compute squared differences
        squared_diff = (forecast - truth) ** 2
        
        # Apply spatial weighting if provided
        if region_weights is not None:
            squared_diff = squared_diff * region_weights
        
        # Compute mean and take square root
        rmse = np.sqrt(squared_diff.mean(dim=['latitude', 'longitude'], skipna=True))
        
        return rmse

@dataclasses.dataclass
class MAE(Metric):
    """Mean Absolute Error metric."""
    
    def compute_chunk(
        self,
        forecast: xr.Dataset,
        truth: xr.Dataset,
        region_weights: Optional[xr.Dataset] = None
    ) -> xr.Dataset:
        """Compute MAE between forecast and truth."""
        # Compute absolute differences
        abs_diff = np.abs(forecast - truth)
        
        # Apply spatial weighting if provided
        if region_weights is not None:
            abs_diff = abs_diff * region_weights
        
        # Compute mean
        mae = abs_diff.mean(dim=['latitude', 'longitude'], skipna=True)
        
        return mae

@dataclasses.dataclass
class Bias(Metric):
    """Bias (mean error) metric."""
    
    def compute_chunk(
        self,
        forecast: xr.Dataset,
        truth: xr.Dataset,
        region_weights: Optional[xr.Dataset] = None
    ) -> xr.Dataset:
        """Compute bias between forecast and truth."""
        # Compute differences
        diff = forecast - truth
        
        # Apply spatial weighting if provided
        if region_weights is not None:
            diff = diff * region_weights
        
        # Compute mean
        bias = diff.mean(dim=['latitude', 'longitude'], skipna=True)
        
        return bias

def get_lat_weights(dataset: xr.Dataset) -> xr.Dataset:
    """
    Compute latitude weights for area-weighted averaging.
    
    This is a key WeatherBench2 utility function.
    """
    # Convert latitude to radians
    lat_rad = np.deg2rad(dataset.latitude)
    
    # Compute cosine weights
    weights = np.cos(lat_rad)
    
    # Create dataset with same structure
    weight_ds = xr.Dataset()
    for var in dataset.data_vars:
        if 'latitude' in dataset[var].dims:
            # Broadcast weights to match variable dimensions
            var_weights = weights
            for dim in dataset[var].dims:
                if dim != 'latitude':
                    var_weights = var_weights.expand_dims(dim)
            weight_ds[var] = var_weights.transpose(*dataset[var].dims)
    
    return weight_ds

def evaluate_forecast(
    forecast: xr.Dataset,
    truth: xr.Dataset,
    metrics: Dict[str, Metric],
    use_lat_weights: bool = True
) -> Dict[str, xr.Dataset]:
    """
    Evaluate forecast against truth using multiple metrics.
    
    This follows the WeatherBench2 evaluation workflow.
    """
    results = {}
    
    # Get latitude weights if requested
    weights = get_lat_weights(truth) if use_lat_weights else None
    
    # Compute each metric
    for metric_name, metric in metrics.items():
        print(f"Computing {metric_name}...")
        results[metric_name] = metric.compute_chunk(forecast, truth, weights)
    
    return results

# Example: Evaluate forecast using WeatherBench2 metrics
print("📈 WeatherBench2 Metrics Evaluation:")

# Create synthetic forecast data that matches truth data structure
# (In practice, this would be real forecast data)
synthetic_forecast = truth_data.copy()
# Add some noise to simulate forecast errors
for var in synthetic_forecast.data_vars:
    noise_scale = 0.1 * synthetic_forecast[var].std()
    noise = np.random.normal(0, noise_scale, synthetic_forecast[var].shape)
    synthetic_forecast[var] = synthetic_forecast[var] + noise

# Define metrics to compute
metrics_dict = {
    'rmse': RMSE(),
    'mae': MAE(),
    'bias': Bias()
}

# Evaluate forecast
evaluation_results = evaluate_forecast(
    synthetic_forecast,
    truth_data,
    metrics_dict,
    use_lat_weights=True
)

# Display results
print("\n📊 Evaluation Results:")
for metric_name, result in evaluation_results.items():
    print(f"\n{metric_name.upper()} Results:")
    for var in result.data_vars:
        mean_score = float(result[var].mean().values)
        print(f"  {var}: {mean_score:.4f}")

# WeatherBench2 Derived Variables - from weatherbench2/derived_variables.py

@dataclasses.dataclass
class DerivedVariable:
    """Base class for derived variables in WeatherBench2."""
    
    def compute(self, dataset: xr.Dataset) -> xr.Dataset:
        """Compute the derived variable from the input dataset."""
        raise NotImplementedError

@dataclasses.dataclass
class WindSpeed(DerivedVariable):
    """Compute wind speed from u and v components."""
    
    u_component: str = 'u_component_of_wind'
    v_component: str = 'v_component_of_wind'
    output_name: str = 'wind_speed'
    
    def compute(self, dataset: xr.Dataset) -> xr.Dataset:
        """Compute wind speed: sqrt(u² + v²)"""
        if self.u_component not in dataset.data_vars:
            raise ValueError(f"Missing u component: {self.u_component}")
        if self.v_component not in dataset.data_vars:
            raise ValueError(f"Missing v component: {self.v_component}")
        
        u = dataset[self.u_component]
        v = dataset[self.v_component]
        
        wind_speed = np.sqrt(u**2 + v**2)
        wind_speed.attrs = {
            'long_name': 'Wind Speed',
            'units': 'm/s',
            'derived_from': f'{self.u_component}, {self.v_component}'
        }
        
        # Add to dataset
        result = dataset.copy()
        result[self.output_name] = wind_speed
        
        return result

@dataclasses.dataclass
class WindSpeed10m(DerivedVariable):
    """Compute 10m wind speed from 10m u and v components."""
    
    u_component: str = '10m_u_component_of_wind'
    v_component: str = '10m_v_component_of_wind'
    output_name: str = '10m_wind_speed'
    
    def compute(self, dataset: xr.Dataset) -> xr.Dataset:
        """Compute 10m wind speed."""
        if self.u_component not in dataset.data_vars:
            raise ValueError(f"Missing 10m u component: {self.u_component}")
        if self.v_component not in dataset.data_vars:
            raise ValueError(f"Missing 10m v component: {self.v_component}")
        
        u = dataset[self.u_component]
        v = dataset[self.v_component]
        
        wind_speed = np.sqrt(u**2 + v**2)
        wind_speed.attrs = {
            'long_name': '10m Wind Speed',
            'units': 'm/s',
            'derived_from': f'{self.u_component}, {self.v_component}'
        }
        
        result = dataset.copy()
        result[self.output_name] = wind_speed
        
        return result

def apply_derived_variables(
    dataset: xr.Dataset,
    derived_variables: Dict[str, DerivedVariable]
) -> xr.Dataset:
    """Apply multiple derived variables to a dataset."""
    result = dataset.copy()
    
    for name, derived_var in derived_variables.items():
        print(f"Computing derived variable: {name}")
        try:
            result = derived_var.compute(result)
        except ValueError as e:
            print(f"Warning: Could not compute {name}: {e}")
            continue
    
    return result

# Example: Compute derived variables
print("🔄 WeatherBench2 Derived Variables:")

# Define derived variables to compute
derived_vars = {
    'wind_speed_10m': WindSpeed10m(),
}

# Apply derived variables
enhanced_data = apply_derived_variables(truth_data, derived_vars)

print(f"\n📊 Original variables: {list(truth_data.data_vars)}")
print(f"📊 Enhanced variables: {list(enhanced_data.data_vars)}")
print(f"📊 New variables added: {set(enhanced_data.data_vars) - set(truth_data.data_vars)}")

# Show statistics for derived variables
if '10m_wind_speed' in enhanced_data.data_vars:
    wind_speed = enhanced_data['10m_wind_speed']
    print(f"\n💨 10m Wind Speed Statistics:")
    print(f"   Mean: {float(wind_speed.mean().values):.2f} m/s")
    print(f"   Max: {float(wind_speed.max().values):.2f} m/s")
    print(f"   Min: {float(wind_speed.min().values):.2f} m/s")

# WeatherBench2 Performance Patterns - chunking and optimization

import dask
import dask.array as da
from typing import Mapping

def optimize_chunks(
    dataset: xr.Dataset,
    target_chunks: Mapping[str, int],
    max_mem_gb: float = 1.0
) -> xr.Dataset:
    """
    Optimize dataset chunking following WeatherBench2 patterns.
    
    This mimics the chunking strategies used in WeatherBench2 scripts.
    """
    print(f"🔧 Optimizing chunks: {target_chunks}")
    print(f"💾 Memory limit: {max_mem_gb} GB")
    
    # Apply chunking
    chunked_ds = dataset.chunk(target_chunks)
    
    # Estimate memory usage
    chunk_memory = estimate_chunk_memory(chunked_ds)
    print(f"📊 Estimated memory per chunk: {chunk_memory:.2f} MB")
    
    if chunk_memory > max_mem_gb * 1024:
        print("⚠️  Warning: Chunk size exceeds memory limit!")
    
    return chunked_ds

def estimate_chunk_memory(dataset: xr.Dataset) -> float:
    """
    Estimate memory usage per chunk in MB.
    
    WeatherBench2 pattern for memory estimation.
    """
    total_elements = 1
    
    # Get chunk sizes for each dimension
    for dim in dataset.dims:
        if hasattr(dataset, 'chunks') and dim in dataset.chunks:
            chunk_sizes = dataset.chunks[dim]
            if isinstance(chunk_sizes, tuple) and len(chunk_sizes) > 0:
                total_elements *= chunk_sizes[0]
            else:
                total_elements *= dataset.dims[dim]
        else:
            total_elements *= dataset.dims[dim]
    
    # Estimate bytes (assuming float64 = 8 bytes per element)
    bytes_per_var = total_elements * 8
    total_bytes = bytes_per_var * len(dataset.data_vars)
    
    return total_bytes / (1024 * 1024)  # Convert to MB

def rechunk_for_computation(
    dataset: xr.Dataset,
    computation_dims: List[str],
    preserve_dims: List[str] = None
) -> xr.Dataset:
    """
    Rechunk dataset for efficient computation.
    
    WeatherBench2 pattern: Rechunk to optimize for specific operations.
    """
    if preserve_dims is None:
        preserve_dims = []
    
    # Create chunking strategy
    chunks = {}
    
    for dim in dataset.dims:
        if dim in computation_dims:
            # Keep computation dimensions unchunked for efficiency
            chunks[dim] = -1
        elif dim in preserve_dims:
            # Preserve existing chunking for these dimensions
            if hasattr(dataset, 'chunks') and dim in dataset.chunks:
                chunks[dim] = dataset.chunks[dim]
        else:
            # Use reasonable default chunking
            dim_size = dataset.dims[dim]
            if dim_size > 100:
                chunks[dim] = min(50, dim_size // 2)
            else:
                chunks[dim] = dim_size
    
    print(f"🔄 Rechunking for computation on {computation_dims}")
    print(f"📦 New chunk strategy: {chunks}")
    
    return dataset.chunk(chunks)

def parallel_apply(
    dataset: xr.Dataset,
    func: Callable[[xr.Dataset], xr.Dataset],
    dim: str = 'time',
    num_workers: int = 2
) -> xr.Dataset:
    """
    Apply function in parallel using Dask.
    
    WeatherBench2 pattern for parallel processing.
    """
    print(f"⚡ Applying function in parallel along {dim} dimension")
    print(f"👥 Using {num_workers} workers")
    
    # Configure Dask
    with dask.config.set(scheduler='threads', num_workers=num_workers):
        # Apply function using xarray's built-in parallelization
        result = xr.apply_ufunc(
            func,
            dataset,
            input_core_dims=[[dim]],
            output_core_dims=[[dim]],
            dask='parallelized',
            output_dtypes=[dataset.dtypes[var] for var in dataset.data_vars]
        )
    
    return result

# WeatherBench2 caching patterns
@functools.lru_cache(maxsize=128)
def cached_computation(data_hash: str, operation: str) -> Any:
    """
    Example of WeatherBench2 caching pattern.
    
    In practice, WeatherBench2 uses more sophisticated caching.
    """
    print(f"🗄️ Computing {operation} for data hash {data_hash[:8]}...")
    # Simulate expensive computation
    import time
    time.sleep(0.1)
    return f"Result for {operation}"

# Example: Performance optimization workflow
print("🚀 WeatherBench2 Performance Optimization:")

# 1. Optimize chunking
optimal_chunks = {
    'time': 10,
    'latitude': 45,
    'longitude': 90
}

chunked_data = optimize_chunks(truth_data, optimal_chunks, max_mem_gb=0.5)

# 2. Rechunk for spatial computation
spatial_rechunked = rechunk_for_computation(
    chunked_data,
    computation_dims=['latitude', 'longitude'],
    preserve_dims=['time']
)

# 3. Example of cached computation
data_hash = str(hash(str(truth_data.dims)))
result1 = cached_computation(data_hash, "mean_calculation")
result2 = cached_computation(data_hash, "mean_calculation")  # Should use cache

print(f"\n🗄️ Cache demonstration:")
print(f"   First call: {result1}")
print(f"   Second call: {result2} (cached)")

# 4. Memory usage comparison
original_memory = estimate_chunk_memory(truth_data)
optimized_memory = estimate_chunk_memory(chunked_data)

print(f"\n💾 Memory Usage Comparison:")
print(f"   Original: {original_memory:.1f} MB per chunk")
print(f"   Optimized: {optimized_memory:.1f} MB per chunk")
print(f"   Reduction: {((original_memory - optimized_memory) / original_memory * 100):.1f}%")

# WeatherBench2 Testing Patterns - from test files throughout the codebase

def validate_dataset_schema(
    dataset: xr.Dataset,
    required_vars: List[str],
    required_dims: List[str],
    check_coordinates: bool = True
) -> bool:
    """
    Validate dataset follows WeatherBench2 schema conventions.
    
    This follows WeatherBench2 validation patterns.
    """
    errors = []
    
    # Check required variables
    missing_vars = [var for var in required_vars if var not in dataset.data_vars]
    if missing_vars:
        errors.append(f"Missing variables: {missing_vars}")
    
    # Check required dimensions
    missing_dims = [dim for dim in required_dims if dim not in dataset.dims]
    if missing_dims:
        errors.append(f"Missing dimensions: {missing_dims}")
    
    # Check coordinate ranges
    if check_coordinates:
        if 'latitude' in dataset.coords:
            lat_min, lat_max = float(dataset.latitude.min()), float(dataset.latitude.max())
            if lat_min < -90 or lat_max > 90:
                errors.append(f"Invalid latitude range: [{lat_min:.1f}, {lat_max:.1f}]")
        
        if 'longitude' in dataset.coords:
            lon_min, lon_max = float(dataset.longitude.min()), float(dataset.longitude.max())
            if lon_min < -180 or lon_max > 360:
                errors.append(f"Invalid longitude range: [{lon_min:.1f}, {lon_max:.1f}]")
    
    # Check for NaN values
    for var in dataset.data_vars:
        if dataset[var].isnull().any():
            nan_count = int(dataset[var].isnull().sum())
            total_count = int(dataset[var].size)
            nan_percent = (nan_count / total_count) * 100
            if nan_percent > 10:  # More than 10% NaN is concerning
                errors.append(f"Variable {var} has {nan_percent:.1f}% NaN values")
    
    if errors:
        print("❌ Dataset validation failed:")
        for error in errors:
            print(f"   • {error}")
        return False
    else:
        print("✅ Dataset validation passed")
        return True

def assert_datasets_compatible(
    forecast: xr.Dataset,
    truth: xr.Dataset,
    check_time_alignment: bool = True
) -> bool:
    """
    Assert that forecast and truth datasets are compatible for evaluation.
    
    WeatherBench2 pattern for dataset compatibility checking.
    """
    errors = []
    
    # Check spatial coordinates match
    for coord in ['latitude', 'longitude']:
        if coord in forecast.coords and coord in truth.coords:
            if not np.allclose(forecast[coord].values, truth[coord].values, atol=1e-3):
                errors.append(f"Coordinate {coord} mismatch between forecast and truth")
    
    # Check variable compatibility
    common_vars = set(forecast.data_vars) & set(truth.data_vars)
    if not common_vars:
        errors.append("No common variables between forecast and truth")
    
    # Check time alignment if requested
    if check_time_alignment and 'time' in forecast.coords and 'time' in truth.coords:
        # For forecast data, we might need to compute valid_time
        if 'init_time' in forecast.coords and 'lead_time' in forecast.coords:
            # Compute valid time for forecast
            forecast_valid_times = forecast.init_time + forecast.lead_time
            truth_times = truth.time
            
            # Check if there's overlap
            overlap = np.intersect1d(forecast_valid_times.values, truth_times.values)
            if len(overlap) == 0:
                errors.append("No time overlap between forecast and truth")
    
    if errors:
        print("❌ Dataset compatibility check failed:")
        for error in errors:
            print(f"   • {error}")
        return False
    else:
        print("✅ Datasets are compatible")
        return True

def create_test_scenario(
    scenario_name: str,
    variables: List[str],
    time_range: Tuple[str, str],
    spatial_resolution: float = 2.0
) -> Dict[str, xr.Dataset]:
    """
    Create a test scenario with forecast and truth data.
    
    WeatherBench2 pattern for creating test scenarios.
    """
    print(f"🧪 Creating test scenario: {scenario_name}")
    
    # Create truth data
    truth = mock_truth_data(
        variables_2d=variables,
        variables_3d=[],  # Simplified for testing
        time_start=time_range[0],
        time_stop=time_range[1],
        spatial_resolution_deg=spatial_resolution
    )
    
    # Create forecast data
    forecast = mock_forecast_data(
        variables_2d=variables,
        variables_3d=[],
        init_times=[time_range[0]],
        lead_times=['1D', '2D', '3D'],
        spatial_resolution_deg=spatial_resolution,
        ensemble_size=5
    )
    
    return {
        'truth': truth,
        'forecast': forecast,
        'scenario_name': scenario_name
    }

def run_evaluation_test(
    test_data: Dict[str, xr.Dataset],
    metrics: Dict[str, Metric],
    expected_metrics: Optional[Dict[str, float]] = None
) -> bool:
    """
    Run a complete evaluation test.
    
    WeatherBench2 pattern for integration testing.
    """
    print(f"🔬 Running evaluation test: {test_data['scenario_name']}")
    
    truth = test_data['truth']
    forecast = test_data['forecast']
    
    # Validate datasets
    truth_valid = validate_dataset_schema(
        truth,
        required_vars=['2m_temperature'],
        required_dims=['time', 'latitude', 'longitude']
    )
    
    if not truth_valid:
        return False
    
    # For testing, we'll create a simplified forecast that matches truth structure
    # In practice, you'd align forecast and truth properly
    simple_forecast = truth.copy()
    # Add some noise to simulate forecast errors
    for var in simple_forecast.data_vars:
        noise = np.random.normal(0, 0.1, simple_forecast[var].shape)
        simple_forecast[var] = simple_forecast[var] + noise
    
    # Check compatibility
    compatible = assert_datasets_compatible(simple_forecast, truth, check_time_alignment=False)
    
    if not compatible:
        return False
    
    # Run evaluation
    try:
        results = evaluate_forecast(simple_forecast, truth, metrics)
        print(f"✅ Evaluation completed successfully")
        
        # Check expected metrics if provided
        if expected_metrics:
            for metric_name, expected_value in expected_metrics.items():
                if metric_name in results:
                    actual_value = float(results[metric_name]['2m_temperature'].mean())
                    if abs(actual_value - expected_value) > 0.1:
                        print(f"⚠️  Metric {metric_name} outside expected range")
                        print(f"   Expected: {expected_value:.3f}, Got: {actual_value:.3f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Evaluation failed: {e}")
        return False

# Example: Run WeatherBench2 testing workflow
print("🧪 WeatherBench2 Testing Framework:")

# Create test scenario
test_scenario = create_test_scenario(
    scenario_name="Temperature Evaluation Test",
    variables=['2m_temperature'],
    time_range=('2020-01-01', '2020-01-07'),
    spatial_resolution=4.0
)

# Define test metrics
test_metrics = {
    'rmse': RMSE(),
    'mae': MAE()
}

# Run evaluation test
test_passed = run_evaluation_test(
    test_scenario,
    test_metrics,
    expected_metrics={'rmse': 2.0, 'mae': 1.5}  # Example expected values
)

print(f"\n🎯 Test Result: {'PASSED' if test_passed else 'FAILED'}")

# WeatherBench2 Best Practices Summary

def demonstrate_wb2_workflow(
    forecast_path: str = "mock_forecast.zarr",
    truth_path: str = "mock_truth.zarr",
    output_path: str = "evaluation_results.zarr"
) -> Dict[str, Any]:
    """
    Demonstrate a complete WeatherBench2 evaluation workflow.
    
    This combines all the patterns we've learned.
    """
    print("🌍 WeatherBench2 Complete Workflow Demonstration")
    print("=" * 50)
    
    # Step 1: Configuration
    print("\n⚙️ Step 1: Configuration")
    config = Data(
        selection=Selection(
            variables=['2m_temperature', '10m_u_component_of_wind', '10m_v_component_of_wind'],
            time_slice=slice('2020-01-01', '2020-01-31'),
            lat_slice=slice(30, 60),
            lon_slice=slice(-10, 40)
        ),
        paths=Paths(
            forecast=forecast_path,
            obs=truth_path,
            output_dir="./output/",
            output_file_prefix="wb2_demo_"
        )
    )
    print(f"   ✅ Configuration created for {len(config.selection.variables)} variables")
    
    # Step 2: Load and validate data
    print("\n📊 Step 2: Data Loading & Validation")
    truth_data = mock_truth_data(
        variables_2d=config.selection.variables,
        variables_3d=[],
        time_start='2020-01-01',
        time_stop='2020-01-31',
        spatial_resolution_deg=1.0
    )
    
    # Apply selection
    selected_truth = apply_selection(truth_data, config.selection)
    
    # Validate
    validation_passed = validate_dataset_schema(
        selected_truth,
        required_vars=config.selection.variables,
        required_dims=['time', 'latitude', 'longitude']
    )
    
    if not validation_passed:
        raise ValueError("Data validation failed")
    
    # Step 3: Compute derived variables
    print("\n🔄 Step 3: Derived Variables")
    derived_vars = {
        'wind_speed_10m': WindSpeed10m()
    }
    
    enhanced_truth = apply_derived_variables(selected_truth, derived_vars)
    print(f"   ✅ Added {len(set(enhanced_truth.data_vars) - set(selected_truth.data_vars))} derived variables")
    
    # Step 4: Performance optimization
    print("\n🚀 Step 4: Performance Optimization")
    optimal_chunks = {
        'time': 10,
        'latitude': 15,
        'longitude': 25
    }
    
    optimized_truth = optimize_chunks(enhanced_truth, optimal_chunks)
    print(f"   ✅ Optimized chunking applied")
    
    # Step 5: Create synthetic forecast for evaluation
    print("\n🔮 Step 5: Forecast Preparation")
    synthetic_forecast = optimized_truth.copy()
    
    # Add realistic forecast errors
    for var in synthetic_forecast.data_vars:
        if 'temperature' in var:
            # Temperature errors: ~1-2K
            error_std = 1.5
        elif 'wind' in var:
            # Wind errors: ~1-2 m/s
            error_std = 1.0
        else:
            error_std = 0.1 * float(synthetic_forecast[var].std())
        
        noise = np.random.normal(0, error_std, synthetic_forecast[var].shape)
        synthetic_forecast[var] = synthetic_forecast[var] + noise
    
    print(f"   ✅ Synthetic forecast created with realistic errors")
    
    # Step 6: Evaluation
    print("\n📈 Step 6: Evaluation")
    metrics = {
        'rmse': RMSE(),
        'mae': MAE(),
        'bias': Bias()
    }
    
    evaluation_results = evaluate_forecast(
        synthetic_forecast,
        optimized_truth,
        metrics,
        use_lat_weights=True
    )
    
    print(f"   ✅ Evaluation completed for {len(metrics)} metrics")
    
    # Step 7: Results summary
    print("\n📋 Step 7: Results Summary")
    summary = {}
    
    for metric_name, result in evaluation_results.items():
        metric_summary = {}
        for var in result.data_vars:
            values = result[var]
            metric_summary[var] = {
                'mean': float(values.mean()),
                'std': float(values.std()),
                'min': float(values.min()),
                'max': float(values.max())
            }
        summary[metric_name] = metric_summary
    
    # Display key results
    print("\n🎯 Key Results:")
    for metric_name in ['rmse', 'mae']:
        if metric_name in summary:
            temp_score = summary[metric_name].get('2m_temperature', {}).get('mean', 0)
            wind_score = summary[metric_name].get('10m_wind_speed', {}).get('mean', 0)
            print(f"   {metric_name.upper()}:")
            print(f"     Temperature: {temp_score:.3f} K")
            if wind_score > 0:
                print(f"     Wind Speed: {wind_score:.3f} m/s")
    
    return {
        'config': config,
        'data_shape': dict(optimized_truth.dims),
        'variables_evaluated': list(optimized_truth.data_vars),
        'metrics_computed': list(metrics.keys()),
        'evaluation_results': summary,
        'workflow_status': 'completed'
    }

# Run the complete workflow demonstration
workflow_results = demonstrate_wb2_workflow()

print("\n" + "=" * 60)
print("🎉 WeatherBench2 Workflow Completed Successfully!")
print("=" * 60)
print(f"📊 Data processed: {workflow_results['data_shape']}")
print(f"🔬 Variables evaluated: {len(workflow_results['variables_evaluated'])}")
print(f"📈 Metrics computed: {len(workflow_results['metrics_computed'])}")
print(f"✅ Status: {workflow_results['workflow_status']}")