#!/usr/bin/env python3
"""
Custom style guide generator for weatherbench2 codebase using Gemini API.
This script analyzes Python files in the codebase and generates a style guide.
"""

import os
import glob
import json
import requests
from pathlib import Path
from typing import List, Dict, Any

# Gemini API configuration
GEMINI_API_KEY = "AIzaSyArbpzocT51D5_DO6jgHFh6ggaNv0Q1gs8"
GEMINI_API_URL = "https://generativelanguage.googleapis.com/v1/models/gemini-1.5-flash-8b:generateContent"

def collect_python_files(directory: str, max_files: int = 20) -> List[str]:
    """Collect Python files from the directory."""
    python_files = []
    
    # Get all .py files recursively
    for root, dirs, files in os.walk(directory):
        # Skip hidden directories and __pycache__
        dirs[:] = [d for d in dirs if not d.startswith('.') and d != '__pycache__']
        
        for file in files:
            if file.endswith('.py') and not file.startswith('.'):
                file_path = os.path.join(root, file)
                python_files.append(file_path)
                
                if len(python_files) >= max_files:
                    return python_files
    
    return python_files

def read_file_content(file_path: str, max_lines: int = 100) -> str:
    """Read file content with line limit."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            # Take first max_lines lines
            content = ''.join(lines[:max_lines])
            if len(lines) > max_lines:
                content += f"\n... (truncated, total {len(lines)} lines)"
            return content
    except Exception as e:
        return f"Error reading file: {e}"

def analyze_code_with_gemini(code_samples: Dict[str, str]) -> str:
    """Send code samples to Gemini for analysis."""
    
    # Prepare the prompt
    prompt = """
Analyze the following Python code samples from a weather benchmarking project and generate a comprehensive style guide. 
Focus on the actual patterns used in this codebase, not generic Python style guides.

Please analyze these aspects:
1. Import organization and conventions
2. Class and function naming patterns
3. Type annotation usage
4. Docstring style and format
5. Code organization and structure
6. Error handling patterns
7. Testing conventions
8. Configuration and data class patterns
9. Any unique patterns specific to this codebase

Here are the code samples:

"""
    
    for file_path, content in code_samples.items():
        prompt += f"\n--- {file_path} ---\n{content}\n"
    
    prompt += """

Based on these code samples, generate a detailed style guide in Markdown format that captures the actual coding patterns used in this project. Include specific examples from the code where relevant.
"""

    # Prepare the request
    headers = {
        'Content-Type': 'application/json',
    }
    
    data = {
        "contents": [{
            "parts": [{
                "text": prompt
            }]
        }]
    }
    
    try:
        response = requests.post(
            f"{GEMINI_API_URL}?key={GEMINI_API_KEY}",
            headers=headers,
            json=data,
            timeout=60
        )
        
        if response.status_code == 200:
            result = response.json()
            if 'candidates' in result and len(result['candidates']) > 0:
                return result['candidates'][0]['content']['parts'][0]['text']
            else:
                return "Error: No response from Gemini API"
        else:
            return f"Error: API request failed with status {response.status_code}: {response.text}"
            
    except Exception as e:
        return f"Error calling Gemini API: {e}"

def main():
    """Main function to generate style guide."""
    print("🔍 Analyzing weatherbench2 codebase...")
    
    # Collect Python files
    current_dir = os.getcwd()
    python_files = collect_python_files(current_dir)
    
    print(f"📁 Found {len(python_files)} Python files")
    
    # Read code samples
    code_samples = {}
    for file_path in python_files[:15]:  # Limit to first 15 files to avoid token limits
        relative_path = os.path.relpath(file_path, current_dir)
        content = read_file_content(file_path)
        code_samples[relative_path] = content
        print(f"📄 Analyzed: {relative_path}")
    
    print("🤖 Sending to Gemini for analysis...")
    
    # Analyze with Gemini
    style_guide = analyze_code_with_gemini(code_samples)
    
    # Create output directory
    output_dir = Path("context42")
    output_dir.mkdir(exist_ok=True)
    
    # Save style guide
    output_file = output_dir / "py.md"
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(style_guide)
    
    print(f"✅ Style guide generated: {output_file}")
    print(f"📊 Analyzed {len(code_samples)} Python files")

if __name__ == "__main__":
    main()
