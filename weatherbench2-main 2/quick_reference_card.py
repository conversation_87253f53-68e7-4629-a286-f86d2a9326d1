#!/usr/bin/env python3
"""
🚀 WEATHERBENCH2 QUICK REFERENCE CARD
=====================================

Essential patterns for weather data analysis and coding interviews.
Copy-paste ready code snippets for common tasks.

Author: WeatherBench2 Tutorial Series
Usage: Keep this file open during interviews for quick reference
"""

import numpy as np
import pandas as pd
import xarray as xr
from collections import defaultdict, Counter, deque
import dataclasses
from typing import List, Dict, Optional, Tuple
from datetime import datetime, timedelta

# ============================================================================
# 🐍 PYTHON ESSENTIALS - MOST COMMON PATTERNS
# ============================================================================

def python_essentials():
    """Essential Python patterns for interviews"""
    
    # List comprehensions (use these!)
    temps = [20, 22, 21, 23, 25]
    hot_temps = [t for t in temps if t > 22]
    fahrenheit = [t * 9/5 + 32 for t in temps]
    
    # Dictionary operations
    station_data = {'NYC': 22.5, 'LA': 28.1, 'CHI': 15.3}
    hot_cities = {city: temp for city, temp in station_data.items() if temp > 20}
    
    # Grouping with defaultdict
    readings = [
        {'station': 'NYC', 'temp': 22.5},
        {'station': 'NYC', 'temp': 23.1},
        {'station': 'LA', 'temp': 28.1}
    ]
    
    by_station = defaultdict(list)
    for reading in readings:
        by_station[reading['station']].append(reading['temp'])
    
    # Calculate averages
    averages = {station: sum(temps)/len(temps) 
                for station, temps in by_station.items()}
    
    return averages

def safe_parsing():
    """Robust data parsing pattern"""
    def parse_weather_line(line):
        try:
            parts = line.strip().split(',')
            if len(parts) != 4:
                return None
            
            station, temp_str, humidity_str, pressure_str = parts
            return {
                'station': station,
                'temperature': float(temp_str),
                'humidity': float(humidity_str),
                'pressure': float(pressure_str)
            }
        except (ValueError, IndexError):
            return None
    
    # Process file with error handling
    def process_weather_file(lines):
        valid_readings = []
        error_count = 0
        
        for line in lines:
            reading = parse_weather_line(line)
            if reading:
                valid_readings.append(reading)
            else:
                error_count += 1
        
        return valid_readings, error_count
    
    return process_weather_file

# ============================================================================
# 📊 XARRAY QUICK PATTERNS
# ============================================================================

def xarray_essentials():
    """Essential xarray patterns"""
    
    # Load and basic operations
    ds = xr.tutorial.load_dataset("air_temperature")
    temp = ds['air']
    
    # Selection patterns
    point_data = temp.sel(lat=40, lon=250, method='nearest')
    time_slice = temp.sel(time='2013-01-01')
    region = temp.sel(lat=slice(60, 40), lon=slice(200, 250))
    
    # Computations
    temp_celsius = temp - 273.15
    daily_mean = temp.resample(time='D').mean()
    spatial_mean = temp.mean(dim=['lat', 'lon'])
    
    # GroupBy operations
    monthly_mean = temp.groupby('time.month').mean()
    anomalies = temp.groupby('time.month') - monthly_mean
    
    # Area-weighted averaging
    weights = np.cos(np.deg2rad(temp.lat))
    global_mean = temp.weighted(weights).mean(dim=['lat', 'lon'])
    
    return global_mean

def xarray_advanced():
    """Advanced xarray patterns"""
    
    # Custom functions with apply_ufunc
    def custom_calculation(data):
        return np.where(data > data.mean(), data * 1.1, data * 0.9)
    
    def apply_custom_function(da):
        return xr.apply_ufunc(
            custom_calculation,
            da,
            input_core_dims=[[]],
            output_core_dims=[[]],
            dask='allowed'
        )
    
    # Interpolation
    def regrid_data(data, new_lats, new_lons):
        return data.interp(lat=new_lats, lon=new_lons, method='linear')
    
    return apply_custom_function, regrid_data

# ============================================================================
# 🌍 SPATIAL CALCULATIONS
# ============================================================================

def spatial_essentials():
    """Essential spatial calculations"""
    
    def haversine_distance(lat1, lon1, lat2, lon2):
        """Great circle distance in kilometers"""
        R = 6371.0  # Earth radius in km
        
        # Convert to radians
        lat1, lon1, lat2, lon2 = map(np.radians, [lat1, lon1, lat2, lon2])
        
        # Haversine formula
        dlat = lat2 - lat1
        dlon = lon2 - lon1
        a = (np.sin(dlat/2)**2 + 
             np.cos(lat1) * np.cos(lat2) * np.sin(dlon/2)**2)
        c = 2 * np.arcsin(np.sqrt(a))
        
        return R * c
    
    def find_nearest_stations(target_lat, target_lon, station_coords, k=3):
        """Find k nearest weather stations"""
        distances = []
        for i, (lat, lon) in enumerate(station_coords):
            dist = haversine_distance(target_lat, target_lon, lat, lon)
            distances.append((dist, i))
        
        distances.sort()
        return distances[:k]
    
    def grid_cell_area(lat, dlat=2.5, dlon=2.5):
        """Calculate grid cell area in km²"""
        R = 6371.0  # Earth radius in km
        lat_rad = np.radians(lat)
        dlat_rad = np.radians(dlat)
        dlon_rad = np.radians(dlon)
        
        area = (R**2 * np.abs(np.sin(lat_rad + dlat_rad/2) - 
                              np.sin(lat_rad - dlat_rad/2)) * 
                np.abs(dlon_rad))
        return area
    
    return haversine_distance, find_nearest_stations, grid_cell_area

# ============================================================================
# 🌤️ WEATHER-SPECIFIC CALCULATIONS
# ============================================================================

def weather_calculations():
    """Common weather calculations"""
    
    def celsius_to_fahrenheit(celsius):
        return celsius * 9/5 + 32
    
    def kelvin_to_celsius(kelvin):
        return kelvin - 273.15
    
    def wind_speed(u_component, v_component):
        return np.sqrt(u_component**2 + v_component**2)
    
    def wind_direction(u_component, v_component):
        """Wind direction in degrees (meteorological convention)"""
        direction = np.degrees(np.arctan2(-u_component, -v_component))
        return (direction + 360) % 360
    
    def heat_index(temp_f, humidity):
        """Heat index in Fahrenheit (simplified formula)"""
        if temp_f < 80:
            return temp_f
        
        hi = (0.5 * (temp_f + 61.0 + ((temp_f - 68.0) * 1.2) + 
                     (humidity * 0.094)))
        
        if hi > 80:
            # More complex formula for higher temperatures
            hi = (-42.379 + 2.04901523 * temp_f + 10.14333127 * humidity -
                  0.22475541 * temp_f * humidity - 0.00683783 * temp_f**2 -
                  0.05481717 * humidity**2 + 0.00122874 * temp_f**2 * humidity +
                  0.00085282 * temp_f * humidity**2 - 
                  0.00000199 * temp_f**2 * humidity**2)
        
        return hi
    
    def dewpoint_from_temp_humidity(temp_c, humidity):
        """Calculate dewpoint from temperature and humidity"""
        a = 17.27
        b = 237.7
        alpha = ((a * temp_c) / (b + temp_c)) + np.log(humidity / 100.0)
        return (b * alpha) / (a - alpha)
    
    return {
        'celsius_to_fahrenheit': celsius_to_fahrenheit,
        'kelvin_to_celsius': kelvin_to_celsius,
        'wind_speed': wind_speed,
        'wind_direction': wind_direction,
        'heat_index': heat_index,
        'dewpoint_from_temp_humidity': dewpoint_from_temp_humidity
    }

# ============================================================================
# 🔧 PERFORMANCE PATTERNS
# ============================================================================

def performance_patterns():
    """High-performance data processing patterns"""
    
    def efficient_grouping(data, key_func):
        """Efficient grouping using defaultdict"""
        groups = defaultdict(list)
        for item in data:
            key = key_func(item)
            groups[key].append(item)
        return dict(groups)
    
    def sliding_window_efficient(data, window_size):
        """Memory-efficient sliding window"""
        window = deque(maxlen=window_size)
        results = []
        
        for value in data:
            window.append(value)
            if len(window) == window_size:
                results.append(sum(window) / len(window))
        
        return results
    
    def batch_processor(data, batch_size=1000):
        """Process data in batches to manage memory"""
        for i in range(0, len(data), batch_size):
            batch = data[i:i + batch_size]
            yield batch
    
    def vectorized_operations(temps_celsius):
        """Use NumPy for vectorized operations"""
        # Convert all temperatures at once
        temps_fahrenheit = temps_celsius * 9/5 + 32
        
        # Statistical operations
        stats = {
            'mean': np.mean(temps_celsius),
            'std': np.std(temps_celsius),
            'min': np.min(temps_celsius),
            'max': np.max(temps_celsius),
            'percentiles': np.percentile(temps_celsius, [25, 50, 75])
        }
        
        return temps_fahrenheit, stats
    
    return {
        'efficient_grouping': efficient_grouping,
        'sliding_window_efficient': sliding_window_efficient,
        'batch_processor': batch_processor,
        'vectorized_operations': vectorized_operations
    }

# ============================================================================
# 🎯 INTERVIEW PROBLEM TEMPLATES
# ============================================================================

def interview_templates():
    """Common interview problem templates"""
    
    def find_temperature_patterns(temperatures, pattern_length=3):
        """Find increasing/decreasing temperature patterns"""
        increasing_patterns = []
        decreasing_patterns = []
        
        current_inc = [temperatures[0]]
        current_dec = [temperatures[0]]
        
        for i in range(1, len(temperatures)):
            # Check increasing pattern
            if temperatures[i] > temperatures[i-1]:
                current_inc.append(temperatures[i])
                current_dec = [temperatures[i]]
            # Check decreasing pattern
            elif temperatures[i] < temperatures[i-1]:
                current_dec.append(temperatures[i])
                if len(current_inc) >= pattern_length:
                    increasing_patterns.append(current_inc.copy())
                current_inc = [temperatures[i]]
            else:
                # Equal temperatures break patterns
                if len(current_inc) >= pattern_length:
                    increasing_patterns.append(current_inc.copy())
                if len(current_dec) >= pattern_length:
                    decreasing_patterns.append(current_dec.copy())
                current_inc = [temperatures[i]]
                current_dec = [temperatures[i]]
        
        # Check final patterns
        if len(current_inc) >= pattern_length:
            increasing_patterns.append(current_inc)
        if len(current_dec) >= pattern_length:
            decreasing_patterns.append(current_dec)
        
        return increasing_patterns, decreasing_patterns
    
    def analyze_weather_extremes(data, temp_threshold=30, pressure_threshold=1000):
        """Analyze extreme weather conditions"""
        extremes = {
            'hot_days': [],
            'low_pressure': [],
            'combined_extremes': []
        }
        
        for reading in data:
            temp = reading.get('temperature', 0)
            pressure = reading.get('pressure', 1013)
            
            is_hot = temp > temp_threshold
            is_low_pressure = pressure < pressure_threshold
            
            if is_hot:
                extremes['hot_days'].append(reading)
            
            if is_low_pressure:
                extremes['low_pressure'].append(reading)
            
            if is_hot and is_low_pressure:
                extremes['combined_extremes'].append(reading)
        
        return extremes
    
    def calculate_station_statistics(readings):
        """Calculate comprehensive statistics for weather stations"""
        station_stats = defaultdict(lambda: {
            'temperatures': [],
            'count': 0,
            'first_reading': None,
            'last_reading': None
        })
        
        # Collect data
        for reading in readings:
            station = reading['station']
            temp = reading['temperature']
            timestamp = reading.get('timestamp', '')
            
            station_stats[station]['temperatures'].append(temp)
            station_stats[station]['count'] += 1
            
            if station_stats[station]['first_reading'] is None:
                station_stats[station]['first_reading'] = timestamp
            station_stats[station]['last_reading'] = timestamp
        
        # Calculate statistics
        final_stats = {}
        for station, data in station_stats.items():
            temps = data['temperatures']
            final_stats[station] = {
                'count': data['count'],
                'mean_temp': sum(temps) / len(temps),
                'min_temp': min(temps),
                'max_temp': max(temps),
                'temp_range': max(temps) - min(temps),
                'std_temp': np.std(temps),
                'first_reading': data['first_reading'],
                'last_reading': data['last_reading']
            }
        
        return final_stats
    
    return {
        'find_temperature_patterns': find_temperature_patterns,
        'analyze_weather_extremes': analyze_weather_extremes,
        'calculate_station_statistics': calculate_station_statistics
    }

# ============================================================================
# 🚀 QUICK TEST FUNCTION
# ============================================================================

def quick_test():
    """Quick test of all patterns"""
    print("🚀 Testing Quick Reference Patterns...")
    
    # Test Python essentials
    averages = python_essentials()
    print(f"✅ Python essentials: {averages}")
    
    # Test spatial calculations
    haversine, find_nearest, grid_area = spatial_essentials()
    distance = haversine(40.7, -74.0, 34.0, -118.2)  # NYC to LA
    print(f"✅ NYC to LA distance: {distance:.0f} km")
    
    # Test weather calculations
    weather_funcs = weather_calculations()
    temp_f = weather_funcs['celsius_to_fahrenheit'](25)
    print(f"✅ 25°C = {temp_f:.1f}°F")
    
    # Test performance patterns
    perf_funcs = performance_patterns()
    test_data = list(range(100))
    moving_avg = perf_funcs['sliding_window_efficient'](test_data, 5)
    print(f"✅ Moving average calculated: {len(moving_avg)} points")
    
    print("🎉 All patterns working correctly!")

if __name__ == "__main__":
    quick_test()
