"""
INTERACTIVE DEBUGGING TEST - Gridmatic Prep
==========================================

Test yourself on debugging and testing skills
Inspired by WeatherBench2 patterns
"""

import numpy as np
import xarray as xr
import pandas as pd
from typing import Dict, List, Tuple, Optional
import time


# ============================================================================
# TEST 1: FIND THE BUGS (Multiple Choice)
# ============================================================================

print("🐛 DEBUGGING SKILLS TEST")
print("=" * 50)

def test_question_1():
    print("\nQUESTION 1: Bug Identification")
    print("-" * 30)
    
    code = '''
def calculate_daily_average(temperatures):
    total = 0
    for temp in temperatures:
        total += temp
    return total / len(temperatures)
    '''
    
    print("Code:")
    print(code)
    print("What's the main bug?")
    print("A) No type hints")
    print("B) Division by zero if temperatures is empty") 
    print("C) Should use sum() instead of loop")
    print("D) Nothing wrong")
    
    correct_answer = "B"
    explanation = "If temperatures is an empty list, len(temperatures) = 0, causing ZeroDivisionError"
    
    return correct_answer, explanation

def test_question_2():
    print("\nQUESTION 2: XArray Bug")
    print("-" * 30)
    
    code = '''
def analyze_weather(ds):
    # Select January data
    jan_data = ds.sel(time='2023-01')
    
    # Calculate correlation
    temp = jan_data['temperature'].values
    pressure = jan_data['pressure'].values
    correlation = np.corrcoef(temp, pressure)[0, 1]
    
    return correlation
    '''
    
    print("Code:")
    print(code)
    print("What could go wrong?")
    print("A) sel() needs method='nearest'")
    print("B) Need to flatten arrays for corrcoef")
    print("C) Should check if variables exist")
    print("D) All of the above")
    
    correct_answer = "D"
    explanation = "Multiple issues: sel() might fail without method, corrcoef needs 1D arrays, and variables might not exist"
    
    return correct_answer, explanation

def test_question_3():
    print("\nQUESTION 3: Performance Bug")
    print("-" * 30)
    
    code = '''
def find_station_pairs(readings):
    pairs = []
    for i, record1 in enumerate(readings):
        for j, record2 in enumerate(readings):
            if record1['station'] != record2['station']:
                pairs.append((record1['station'], record2['station']))
    return pairs
    '''
    
    print("Code:")
    print(code)
    print("What's the performance issue?")
    print("A) O(n²) complexity")
    print("B) Creates duplicate pairs")
    print("C) Should use set for unique stations")
    print("D) All of the above")
    
    correct_answer = "D"
    explanation = "Nested loops create O(n²), generates duplicates, and doesn't leverage sets for efficiency"
    
    return correct_answer, explanation

def run_multiple_choice_test():
    questions = [test_question_1, test_question_2, test_question_3]
    score = 0
    
    for i, question_func in enumerate(questions, 1):
        correct, explanation = question_func()
        
        print(f"\nYour answer for Question {i}: ", end="")
        user_input = input().upper().strip()
        
        if user_input == correct:
            print("✅ CORRECT!")
            score += 1
        else:
            print(f"❌ Wrong. Correct answer: {correct}")
        
        print(f"💡 Explanation: {explanation}")
        print("-" * 50)
    
    print(f"\nSCORE: {score}/{len(questions)}")
    return score

# ============================================================================
# TEST 2: PRACTICAL DEBUGGING CHALLENGES  
# ============================================================================

print("\n🔧 PRACTICAL DEBUGGING CHALLENGES")
print("=" * 50)

class BuggyWeatherAnalyzer:
    """This class has bugs - find and describe them!"""
    
    def __init__(self):
        self.data = []
    
    def add_data(self, temperature, station_id):
        # Bug 1: No validation
        self.data.append({
            'temp': temperature,
            'station': station_id
        })
    
    def get_average(self):
        # Bug 2: Division by zero
        total = sum(record['temp'] for record in self.data)
        return total / len(self.data)
    
    def find_outliers(self, threshold=2):
        # Bug 3: Assumes data exists and is numeric
        temps = [record['temp'] for record in self.data]
        mean_temp = sum(temps) / len(temps)
        outliers = []
        
        for record in self.data:
            if abs(record['temp'] - mean_temp) > threshold:
                outliers.append(record)
        
        return outliers

def practical_challenge_1():
    print("\nCHALLENGE 1: Debug the BuggyWeatherAnalyzer")
    print("Look at the class above and identify the bugs.")
    print("Then explain what would happen in each scenario:")
    
    scenarios = [
        "1. Create analyzer and call get_average() immediately",
        "2. Add data with temperature=None",
        "3. Add data with temperature='25.5' (string)",
        "4. Call find_outliers() with empty data"
    ]
    
    for scenario in scenarios:
        print(f"\n{scenario}")
        print("What happens? ", end="")
        user_response = input()
    
    print("\n💡 Correct Analysis:")
    print("1. ZeroDivisionError - empty list")
    print("2. TypeError in sum() - can't add None")
    print("3. TypeError in sum() - string concatenation")
    print("4. ZeroDivisionError in mean calculation")

def buggy_data_processor(data):
    """Find the bugs in this data processing function"""
    
    # Process weather data
    processed = []
    
    for record in data:
        # Bug: No error handling
        temp = float(record['temperature'])
        
        # Bug: Modifying during iteration  
        if temp < -50:
            data.remove(record)
            continue
            
        # Bug: Key might not exist
        station = record['station_id']
        
        processed.append({
            'temp_celsius': temp,
            'temp_fahrenheit': temp * 9/5 + 32,
            'station': station
        })
    
    return processed

def practical_challenge_2():
    print("\nCHALLENGE 2: Debug the data processor")
    print("The buggy_data_processor function has several issues.")
    print("Test it with this problematic data:")
    
    problematic_data = [
        {'temperature': '25.5', 'station_id': 'A01'},
        {'temperature': 'invalid', 'station_id': 'B02'},
        {'temperature': '-60', 'station_id': 'C03'},
        {'temperature': '30', 'station': 'D04'},  # Wrong key
    ]
    
    print("\nWhat errors will occur? (Try to predict before running)")
    
    try:
        result = buggy_data_processor(problematic_data.copy())
        print(f"Result: {result}")
    except Exception as e:
        print(f"Error occurred: {type(e).__name__}: {e}")
    
    print("\n💡 Issues found:")
    print("- ValueError when converting 'invalid' to float")
    print("- Modifying list during iteration causes skipping")
    print("- KeyError when 'station_id' key missing")
    print("- No validation of input data structure")

# ============================================================================
# TEST 3: WRITE TESTS FOR BUGGY CODE
# ============================================================================

def broken_temperature_converter(celsius):
    """Convert Celsius to Fahrenheit - has bugs!"""
    # Bug: No input validation
    fahrenheit = celsius * 9/5 + 32
    return fahrenheit

def test_writing_challenge():
    print("\n📝 TEST WRITING CHALLENGE")
    print("=" * 50)
    
    print("Write tests for the broken_temperature_converter function.")
    print("Consider these test cases:")
    
    test_cases = [
        ("Normal case: 0°C", 0, 32),
        ("Normal case: 100°C", 100, 212),
        ("Negative temperature: -40°C", -40, -40),
        ("Edge case: None input", None, "Should handle gracefully"),
        ("Edge case: String input", "25", "Should handle gracefully"),
        ("Edge case: Very large number", 1e10, "Should handle gracefully")
    ]
    
    print("\nWhich test cases would FAIL with the current implementation?")
    for i, (description, input_val, expected) in enumerate(test_cases, 1):
        print(f"{i}. {description}")
    
    print("\nYour prediction (e.g., '4,5,6'): ", end="")
    user_prediction = input()
    
    print("\n🧪 ACTUAL TEST RESULTS:")
    
    for i, (description, input_val, expected) in enumerate(test_cases, 1):
        try:
            result = broken_temperature_converter(input_val)
            if isinstance(expected, (int, float)) and abs(result - expected) < 0.001:
                status = "✅ PASS"
            else:
                status = f"❓ Returns {result}, expected {expected}"
        except Exception as e:
            status = f"❌ FAIL: {type(e).__name__}"
        
        print(f"{i}. {description}: {status}")
    
    print("\n💡 Tests 4, 5, 6 would fail - no input validation!")

# ============================================================================
# TEST 4: PERFORMANCE DEBUGGING
# ============================================================================

def slow_weather_analysis(readings):
    """Intentionally slow implementation"""
    station_stats = {}
    
    # Inefficient: O(n²) for each statistic
    for record in readings:
        station = record['station']
        if station not in station_stats:
            # Recalculate everything for each record
            station_readings = [r for r in readings if r['station'] == station]
            temps = [r['temp'] for r in station_readings]
            
            station_stats[station] = {
                'count': len(temps),
                'sum': sum(temps),
                'avg': sum(temps) / len(temps) if temps else 0,
                'min': min(temps) if temps else 0,
                'max': max(temps) if temps else 0
            }
    
    return station_stats

def fast_weather_analysis(readings):
    """Optimized version"""
    from collections import defaultdict
    
    station_temps = defaultdict(list)
    
    # Single pass to group data
    for record in readings:
        station_temps[record['station']].append(record['temp'])
    
    # Calculate stats once per station
    station_stats = {}
    for station, temps in station_temps.items():
        if temps:
            station_stats[station] = {
                'count': len(temps),
                'sum': sum(temps),
                'avg': sum(temps) / len(temps),
                'min': min(temps),
                'max': max(temps)
            }
    
    return station_stats

def performance_challenge():
    print("\n⚡ PERFORMANCE DEBUGGING CHALLENGE")
    print("=" * 50)
    
    # Create test data
    test_data = []
    for i in range(1000):
        test_data.append({
            'station': f'STATION_{i % 10}',
            'temp': 20 + (i % 20)
        })
    
    print("Testing with 1000 records across 10 stations...")
    
    # Time slow version
    start_time = time.time()
    slow_result = slow_weather_analysis(test_data)
    slow_time = time.time() - start_time
    
    # Time fast version
    start_time = time.time()
    fast_result = fast_weather_analysis(test_data)
    fast_time = time.time() - start_time
    
    print(f"Slow version: {slow_time:.4f} seconds")
    print(f"Fast version: {fast_time:.4f} seconds")
    print(f"Speedup: {slow_time/fast_time:.1f}x faster")
    
    print("\nWhat optimizations were made?")
    print("1. Eliminated nested loops (O(n²) → O(n))")
    print("2. Single pass data grouping")
    print("3. Used defaultdict for cleaner code")
    print("4. Avoided redundant calculations")

# ============================================================================
# MAIN TEST RUNNER
# ============================================================================

def run_complete_debugging_test():
    """Run the complete debugging skills test"""
    
    print("🎯 COMPLETE DEBUGGING SKILLS TEST")
    print("=" * 60)
    print("This test covers:")
    print("✓ Bug identification")
    print("✓ Practical debugging")
    print("✓ Test writing")
    print("✓ Performance analysis")
    print()
    
    # Test 1: Multiple choice questions
    mc_score = run_multiple_choice_test()
    
    # Test 2: Practical debugging
    practical_challenge_1()
    practical_challenge_2()
    
    # Test 3: Test writing
    test_writing_challenge()
    
    # Test 4: Performance debugging
    performance_challenge()
    
    print("\n" + "=" * 60)
    print("🏆 DEBUGGING TEST COMPLETED!")
    print(f"Multiple choice score: {mc_score}/3")
    
    if mc_score == 3:
        print("🌟 Excellent debugging skills!")
    elif mc_score >= 2:
        print("👍 Good debugging awareness!")
    else:
        print("📚 Keep practicing debugging techniques!")
    
    print("\n🎯 KEY DEBUGGING SKILLS DEMONSTRATED:")
    print("✓ Input validation and error handling")
    print("✓ Edge case identification")
    print("✓ Performance bottleneck recognition")
    print("✓ Test case design")
    print("✓ Bug impact assessment")

if __name__ == "__main__":
    run_complete_debugging_test()
    
    print("\n📋 INTERVIEW PREPARATION SUMMARY:")
    print("=" * 50)
    print("For your Gridmatic interview, focus on:")
    print("1. 🐛 Quickly identifying common bug patterns")
    print("2. 🛡️  Writing defensive code with validation")
    print("3. 🧪 Testing edge cases and error conditions") 
    print("4. ⚡ Recognizing performance issues")
    print("5. 🔍 Systematic debugging approach")
    print("\nYou're ready to handle debugging questions! 💪")