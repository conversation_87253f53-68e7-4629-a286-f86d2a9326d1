---
description: Python Style Guide
globs: "**/*.py"
alwaysApply: false
---

# Weatherbench2 Python Style Guide: Decoding the Developer's DNA

This guide aims to capture the nuanced coding style of the Weatherbench2 project's developer(s), enabling an AI agent to produce contributions indistinguishable from the original authors. Beyond mere syntax, it delves into the underlying philosophy, problem-solving approaches, and subtle preferences that define this codebase's unique character. The style is remarkably consistent across all subdirectories, indicating a strong, well-defined coding philosophy from the outset.

## 1. CORE PHILOSOPHY

The developer(s) prioritize **scalable, data-centric scientific computing** with a strong emphasis on **clarity, configurability, and correctness**. This is achieved through explicit data structures, functional programming paradigms for data transformations, and robust validation.

**Key Principles:**
*   **Scalability:** Processing large, N-dimensional meteorological datasets efficiently using distributed computing (Apache Beam) and optimized data structures (Xarray, Zarr).
*   **Data-Centricity:** Operations are designed around `xarray.Dataset` and `DataArray` objects, treating data as the primary citizen and transformations as functions applied to it.
*   **Configurability:** Scripts are highly parameterized via command-line flags (`absl.flags`), allowing flexible execution without code changes.
*   **Correctness & Reproducibility:** Robust testing, explicit data selection, and clear error handling are paramount to ensure reliable scientific results.
*   **Readability over Obscurity:** While performance is key, code remains readable and idiomatic Python, leveraging established libraries for complex tasks.
*   **Explicitness:** Data structures, variable names, and function signatures clearly convey their purpose and domain.
*   **Modularity:** Code is organized into small, focused modules, each with a clear responsibility.
*   **Robustness:** Assertions and explicit error handling are used to guard against invalid states and unexpected inputs, especially when dealing with scientific data.

## 2. NAMING PATTERNS

Naming is precise, descriptive, and follows a consistent pattern, often incorporating domain-specific terms and common abbreviations.

### 2.1. General Conventions

*   **Constants/Flags:** `ALL_CAPS`
    *   **Rationale:** Standard Python convention for global constants, clearly distinguishing them from variables. For flags, it emphasizes their configurable nature.
    *   **Examples:**
        ```python
        # scripts/compute_averages.py
        INPUT_PATH = flags.DEFINE_string('input_path', None, help='Input Zarr path')
        AVERAGING_DIMS = flags.DEFINE_list('averaging_dims', None, help='Comma delimited list of dimensions to average over.')
        ```
    *   **Anti-Example (Avoid):** `inputPath`, `averagingDims`

*   **Functions/Methods:** `snake_case`
    *   **Rationale:** Adherence to PEP 8 for function and method names, promoting readability.
    *   **Examples:**
        ```python
        # scripts/compute_averages.py
        def _impose_data_selection(ds: xr.Dataset, source_chunks: t.Mapping[str, int]):
            # ...
        def main(argv: list[str]):
            # ...
        ```
    *   **Anti-Example (Avoid):** `imposeDataSelection`, `Main`

*   **Private Helper Functions:** `_snake_case` (prefixed with a single underscore)
    *   **Rationale:** Clearly signals that the function is internal to the module and not part of the public API, guiding usage and refactoring.
    *   **Examples:**
        ```python
        # scripts/compute_averages.py
        def _impose_data_selection(ds: xr.Dataset, source_chunks: t.Mapping[str, int]):
            # ...
        # scripts/compute_derived_variables.py
        def _add_derived_variables(dataset: xr.Dataset, derived_variables: dict[str, dvs.DerivedVariable]):
            # ...
        ```
    *   **Anti-Example (Avoid):** `impose_data_selection` (if intended as internal), `__add_derived_variables` (if not strictly name-mangling)

*   **Classes:** `CamelCase`
    *   **Rationale:** Standard Python convention for class names, improving code clarity and discoverability. Often suffixed with `Config`, `Metric`, `Region`, `Regridder`, `Variable` to denote their type or role.
    *   **Examples:**
        ```python
        # weatherbench2/config.py
        class Selection:
            # ...
        # weatherbench2/metrics.py
        class RMSESqrtBeforeTimeAvg(Metric):
            # ...
        ```
    *   **Anti-Example (Avoid):** `rmse_metric` (snake_case for classes).

*   **Variables and Attributes:** `snake_case`
    *   **Rationale:** Adherence to PEP 8 for variable names, ensuring consistency and readability. Highly descriptive, often including units or physical meaning where relevant.
    *   **Examples:**
        ```python
        # scripts/compute_averages.py
        source_dataset, source_chunks = xbeam.open_zarr(INPUT_PATH.value)
        weights = metrics.get_lat_weights(source_dataset)
        ```
    *   **Anti-Example (Avoid):** `SourceDataset`, `source_Ds`, `sourceDataset`

### 2.2. Abbreviations and Domain Terms

*   **Common Abbreviations:** Short, widely understood abbreviations are used, especially for frequently referenced objects or concepts.
    *   **Rationale:** Reduces verbosity without sacrificing clarity, particularly beneficial in scientific code where variable names can become long.
    *   **Examples:**
        ```python
        # scripts/compute_averages.py
        ds: xr.Dataset # 'ds' for dataset
        obs: xr.Dataset # 'obs' for observations
        dv: dvs.DerivedVariable # 'dv' for derived variable
        rsmp_key: xbeam.Key # 'rsmp' for resampled
        ```

*   **Domain-Specific Terminology:** Meteorological and scientific terms are used directly and consistently.
    *   **Rationale:** Enhances domain experts' understanding, aligns code with scientific literature, and reduces cognitive load for those familiar with the field.
    *   **Examples:**
        ```python
        # scripts/compute_averages.py
        AVERAGING_DIMS = flags.DEFINE_list('averaging_dims', ...)
        # weatherbench2/derived_variables.py
        derived_variables = [ 'wind_speed', '10m_wind_speed', 'divergence', ...]
        # weatherbench2/derived_variables.py
        zonal_wavenumber # a dimension name
        ```

### 2.3. Dimensions and Coordinates (xarray specific)

*   **Convention:** `snake_case`. Standardized names for common meteorological dimensions (`latitude`, `longitude`, `level`, `time`, `lead_time`, `init_time`, `dayofyear`, `hour`, `realization`).
*   **Rationale:** Consistency across `xarray.Dataset` objects is paramount for interoperability and ease of data manipulation.
*   **Examples:**
        ```python
        # weatherbench2/evaluation.py
        forecast.coords['valid_time']
        # weatherbench2/metrics.py
        forecast.mean(ensemble_dim, skipna=skipna)
        # weatherbench2/schema.py
        dims=('time', 'level', 'longitude', 'latitude')
        ```
*   **Anti-pattern (NOT used):** `lat`, `lon`, `p` (unless aliased for brevity in a very local context, but the full name is preferred).

## 3. CODE ORGANIZATION

Code is structured logically from the file level down to function internals, emphasizing modularity and pipeline-oriented processing.

### 3.1. Project Structure

*   **Convention:** Flat module structure within the `weatherbench2` directory. Related functionalities are grouped into distinct `.py` files. Test files (`_test.py`) are placed in the same directory as the code they test.
*   **Rationale:** Simplifies imports and reduces cognitive load for navigating the codebase. Promotes modularity, reusability, and easier navigation.
*   **Examples:**
        ```
        weatherbench2/
        ├── __init__.py
        ├── config.py
        ├── derived_variables.py
        ├── evaluation.py
        ├── metrics.py
        # ...
        scripts/
        ├── compute_averages.py
        ├── compute_averages_test.py
        # ...
        ```
*   **Anti-pattern (NOT used):** Deeply nested directories like `weatherbench2/core/data/processing/`.

### 3.2. Module Responsibilities

*   **Convention:** Each module has a single, well-defined responsibility.
*   **Rationale:** Promotes high cohesion and low coupling, making modules easier to understand, test, and maintain independently.
*   **Examples:**
    *   `weatherbench2/config.py`: Defines data structures for configuration.
    *   `weatherbench2/derived_variables.py`: Defines classes for computing new variables from existing ones.
    *   `scripts/compute_climatology.py`: Dedicated to computing and saving climatology.

### 3.3. Import Statements

*   **Convention:** Imports are organized into distinct groups, typically in this order:
    1.  Standard library imports (e.g., `typing`, `collections`, `math`).
    2.  Third-party library imports (e.g., `absl`, `apache_beam`, `numpy`, `xarray`).
    3.  Local project imports (e.g., `weatherbench2` submodules, relative imports).
    Imports are typically absolute from the project root (`weatherbench2`). Aliases are used for common libraries (`import typing as t`, `import apache_beam as beam`, `import xarray as xr`, `import xarray_beam as xbeam`, `import pandas as pd`).
*   **Rationale:** Improves readability, makes dependencies clear, and helps prevent import conflicts. Consistent aliasing reduces verbosity for frequently used libraries.
*   **Examples:**
        ```python
        # scripts/compute_climatology.py
        import ast
        import functools
        from typing import Callable, Optional, Union

        from absl import app
        from absl import flags
        import apache_beam as beam
        import numpy as np
        from weatherbench2 import flag_utils
        from weatherbench2 import utils
        import xarray as xr
        import xarray_beam as xbeam
        ```
*   **Anti-pattern (NOT used):** Relative imports (`from . import utils`).

### 3.4. Class and Function Structure

*   **Convention:**
    *   `main` function serves as the primary entry point for execution, typically orchestrated by `absl.app.run(main)`.
    *   Complex logic within `main` is broken down into smaller, focused helper functions, often prefixed with `_` if internal.
    *   `dataclasses` are heavily used for data-holding classes, often with default factories for optional fields.
    *   Methods within classes are logically grouped. `__init__` is implicit for dataclasses.
    *   Helper methods are often prefixed with `_`.
    *   Properties (`@property`) are used for computed attributes.
*   **Rationale:** Improves modularity, testability, and reusability of specific logic components. `dataclasses` reduce boilerplate.
*   **Examples:**
        ```python
        # scripts/compute_climatology.py
        class Quantile:
            def __init__(self, quantiles: list[float]):
                # ...
            def compute(self, ds: xr.Dataset, dim: tuple[str], weights: Optional[xr.Dataset] = None):
                # ...
        ```

### 3.5. Code Formatting and Readability

*   **Convention:** General adherence to PEP 8 for whitespace, line length (often using parentheses for continuation), and indentation (4 spaces). Blank lines are used judiciously to separate logical blocks of code, function definitions, and class definitions.
*   **Rationale:** Consistent formatting improves code scanability and maintainability.
*   **Examples:**
        ```python
        # scripts/compute_averages.py
        # pylint: disable=expression-not-assigned

        def _impose_data_selection(
            ds: xr.Dataset,
            source_chunks: t.Mapping[str, int],
        ) -> tuple[xr.Dataset, dict[str, int]]:
          """Select requested subset of data and trim chunks if needed."""
          if VARIABLES.value is not None:
            ds = ds[VARIABLES.value]
          selection = {
              TIME_DIM.value: slice(TIME_START.value, TIME_STOP.value),
          }
          # ...
        ```

## 4. ERROR HANDLING

The developer employs a pragmatic approach to error handling, balancing explicit checks with reliance on `xarray`'s robust behavior. Assertions are common for validating assumptions about data shape and content.

### 4.1. Flag Validation

*   **Convention:** `absl.flags.mark_flag_as_required` is used to ensure critical command-line arguments are provided. Flags are defined with specific types (`DEFINE_string`, `DEFINE_list`, `DEFINE_integer`, `DEFINE_enum`) and often include custom validation logic.
*   **Rationale:** Prevents scripts from running with incomplete configurations, providing immediate feedback to the user. Ensures that inputs conform to expected constraints.
*   **Examples:**
        ```python
        # scripts/compute_averages.py
        if __name__ == '__main__':
          app.run(main)
          flags.mark_flag_as_required(['averaging_dims'])
        # scripts/compute_climatology.py
        if statistic not in ['mean', 'std', 'quantile']:
            raise NotImplementedError(f'stat {statistic} not implemented.')
        ```
*   **Anti-Example (Avoid):** Relying solely on `absl.flags` default type conversion without additional checks for valid ranges or combinations.

### 4.2. Precondition Checks and Assertions

*   **Convention:** `ValueError` is raised for invalid inputs or unexpected states. `assert` statements are used to enforce preconditions or invariants within functions, especially in Beam transforms where data keys or chunk structures are critical.
*   **Rationale:** Provides immediate feedback on incorrect usage or data issues, preventing silent failures. Catches logical errors during development and testing.
*   **Examples:**
        ```python
        # scripts/compute_derived_variables.py
        assert len(key.vars) == 1, key
        # scripts/index_on_valid_time.py
        assert chunk.sizes[INIT] == 1 and chunk.sizes[DELTA] == 1
        ```
*   **Anti-Example (Avoid):** Broad `except` blocks without specific error handling.

### 4.3. Data Consistency Checks (xarray specific)

*   **Convention:** Explicit checks for `xarray.Dataset` dimensions, coordinates, and data consistency (e.g., `np.diff`, `np.allclose`).
*   **Rationale:** Scientific data processing is highly sensitive to data alignment and structure. These checks ensure operations are performed on compatible datasets.
*   **Examples:**
        ```python
        # weatherbench2/evaluation.py
        def _ensure_aligned_grid(
            dataset: xr.Dataset,
            target: xr.Dataset,
            atol: float = 1e-3,
        ) -> xr.Dataset:
          """Ensure that the horizontal coordinates on dataset exactly match target."""
          for coord_name in ['latitude', 'longitude']:
            np.testing.assert_allclose(
                dataset[coord_name].data, target[coord_name].data, atol=atol
            )
        ```

### 4.4. Handling `NaN` values

*   **Convention:** `NaN` values are explicitly handled, often propagated, or conditionally ignored based on the metric's requirements. `skipna` parameters are common in aggregation functions.
*   **Rationale:** `NaN`s are a common occurrence in real-world scientific data and must be managed carefully to avoid incorrect computations.
*   **Examples:**
        ```python
        # weatherbench2/derived_variables.py (PrecipitationAccumulation)
        if self.set_negative_to_zero:
          accumulation = accumulation.where(
              np.logical_or(accumulation >= 0.0, np.isnan(accumulation)), 0.0
          )

        # weatherbench2/metrics.py (_spatial_average)
        return dataset.weighted(weights).mean(
            ["latitude", "longitude"], skipna=skipna
        )
        ```

## 5. STATE MANAGEMENT

State management primarily revolves around `xarray.Dataset` objects, which are treated as immutable data structures for transformations. Configuration is managed through `dataclasses`.

### 5.1. Data Immutability (Functional Transformations)

*   **Convention:** `xarray.Dataset` objects are typically passed as arguments to functions that return new `Dataset` objects with transformations applied, rather than modifying them in place. `copy()` is used when a modification is necessary.
*   **Rationale:** Promotes functional purity, simplifies debugging, and ensures reproducibility of scientific computations. It aligns well with the `xarray` philosophy.
*   **Examples:**
        ```python
        # weatherbench2/evaluation.py (make_latitude_increasing)
        def make_latitude_increasing(dataset: xr.Dataset) -> xr.Dataset:
          """Make sure latitude values are increasing. Flip dataset if necessary."""
          lat = dataset.latitude.values
          if (np.diff(lat) < 0).all():
            reverse_lat = lat[::-1]
            dataset = dataset.sel(latitude=reverse_lat) # .sel returns a new object
          return dataset
        ```
*   **Anti-pattern (NOT used):** Extensive in-place modification of large `xarray.Dataset` objects.

### 5.2. Configuration State

*   **Convention:** `dataclasses` are used to define structured configuration objects (`Selection`, `Paths`, `Data`, `Eval`, `Viz`, `Panel`). These are typically instantiated once and passed around. `absl.flags` define the global configuration for each script.
*   **Rationale:** Provides clear, type-hinted, and immutable (by convention) configuration. Provides a simple and effective way to parameterize script behavior.
*   **Examples:**
        ```python
        # weatherbench2/config.py
        @dataclasses.dataclass
        class Selection:
          variables: t.Sequence[str]
          time_slice: slice
          # ...

        # scripts/compute_averages.py
        INPUT_PATH = flags.DEFINE_string('input_path', None, help='Input Zarr path')
        # ...
        source_dataset, source_chunks = xbeam.open_zarr(INPUT_PATH.value)
        ```
*   **Anti-pattern (NOT used):** Global mutable configuration dictionaries.

### 5.3. Distributed State (Apache Beam)

*   **Convention:** Apache Beam's PCollections are inherently immutable. Data transformations operate on PCollections to produce new ones, ensuring a functional data processing pipeline.
*   **Rationale:** Essential for scalable, fault-tolerant distributed processing, as it allows Beam to optimize execution and re-execute failed stages without side effects.
*   **Examples:**
        ```python
        # scripts/compute_averages.py
        chunked = root | xbeam.DatasetToChunks(...)
        chunked = chunked | beam.MapTuple(...) # 'chunked' is reassigned to a new PCollection
        ```

## 6. API DESIGN

The "API" of these scripts is primarily their command-line interface, defined by `absl.flags`. Internal APIs are data-centric and functional, operating on Xarray datasets.

### 6.1. Command-Line Interface (CLI)

*   **Convention:** Scripts expose their functionality through a comprehensive set of `absl.flags`. Flag names are descriptive and follow `snake_case` (for the flag name itself) and `ALL_CAPS` (for the Python variable holding the flag). Every flag includes a `help` message, often with detailed explanations and examples.
*   **Rationale:** Provides a flexible and user-friendly way to interact with the scripts, enabling complex workflows via shell commands. Essential for usability.
*   **Examples:**
        ```python
        # scripts/compute_averages.py
        flags.DEFINE_string('input_path', None, help='Input Zarr path')
        flags.DEFINE_list('averaging_dims', None, help='Comma delimited list of dimensions to average over.')
        # scripts/compute_climatology.py
        flags.DEFINE_string('frequency', 'hourly', ('Frequency of the computed climatology. "hourly": Compute the climatology per day of year and hour of day. "daily": Compute the climatology per day of year.'))
        ```

### 6.2. Internal APIs (Functions)

*   **Convention:** Functions primarily accept and return `xarray.Dataset` or `xarray.DataArray` objects, or `xarray_beam.Key` and `xarray.Dataset` tuples for Beam transforms. Operations are often encapsulated within classes (e.g., `Metric`, `DerivedVariable`, `Region`, `Regridder`). These classes define a `compute` or `apply` method that performs the core logic.
*   **Rationale:** Aligns with the data-centric nature of the problem domain, making function signatures intuitive for data scientists. Promotes polymorphism and allows for easy extension of functionality.
*   **Examples:**
        ```python
        # scripts/compute_averages.py
        def _impose_data_selection(ds: xr.Dataset, source_chunks: t.Mapping[str, int]) -> tuple[xr.Dataset, dict[str, int]]:
            # ...
        # weatherbench2/metrics.py
        @dataclasses.dataclass
        class RMSESqrtBeforeTimeAvg(Metric):
          def compute_chunk(self, forecast: xr.Dataset, truth: xr.Dataset, ...) -> xr.Dataset:
            # ...
        ```

### 6.3. Standardized Data Schemas

*   **Convention:** `xarray.Dataset` objects adhere to a consistent set of dimension and variable names (`time`, `lead_time`, `latitude`, `longitude`, `level`, `realization`, `geopotential`, `temperature`, etc.). The `schema.py` module explicitly defines and mocks these.
*   **Rationale:** Essential for interoperability between different components. Reduces the need for extensive data validation within each function.
*   **Examples:**
        ```python
        # weatherbench2/schema.py
        ALL_3D_VARIABLES = (
            'geopotential',
            'temperature',
            'u_component_of_wind',
            'v_component_of_wind',
            'specific_humidity',
        )
        # ...
        def mock_truth_data(*, variables_3d: abc.Sequence[str] = ALL_3D_VARIABLES, ...) -> xarray.Dataset:
        ```

## 7. TESTING APPROACH

Testing is comprehensive, focusing on both unit-level correctness and integration-level pipeline validation, with a strong emphasis on programmatic flag configuration.

### 7.1. Test File Structure and Naming

*   **Convention:** Test files are named `[module_name]_test.py` and reside in the same directory as the source code.
*   **Rationale:** Facilitates easy discovery and maintenance of tests alongside the code they validate.
*   **Examples:**
        ```
        scripts/compute_averages.py
        scripts/compute_averages_test.py
        ```

### 7.2. Test Framework and Utilities

*   **Convention:** `absl.testing.absltest` is the primary test framework. `absl.testing.flagsaver` is used extensively to set command-line flags programmatically within tests. `xarray.testing.assert_allclose` / `assert_equal` are used for robust comparison of Xarray datasets. `self.create_tempdir()` is used to create isolated temporary directories for input and output Zarr stores.
*   **Rationale:** Ensures test isolation and reproducibility. Provides domain-appropriate assertion capabilities.
*   **Examples:**
        ```python
        # scripts/compute_averages_test.py
        with flagsaver.flagsaver(
            input_path=input_path,
            output_path=output_path,
            time_start='2020-06-01',
            averaging_dims=['time', 'longitude'],
        ):
          compute_averages.main([])
        # scripts/compute_averages_test.py
        xarray.testing.assert_allclose(output_ds, expected)
        # scripts/compute_averages_test.py
        input_path = self.create_tempdir('source').full_path
        output_path = self.create_tempdir('destination').full_path
        ```
*   **Anti-Example (Avoid):** Modifying `sys.argv` directly or relying on external configuration files for tests.

### 7.3. Test Scope and Coverage

*   **Convention:** Tests often involve the full pipeline flow: mocking input, saving to Zarr, setting flags, calling `main`, loading output, and asserting correctness. Specific tests are included for edge cases.
*   **Rationale:** Validates the end-to-end functionality of the scripts. Ensures robustness and correctness under diverse operating conditions.
*   **Examples:**
        ```python
        # scripts/evaluate_test.py
        class WB2Evaluation(absltest.TestCase):
          def _test(self, use_beam=True, input_chunks=None):
            # ... full pipeline setup and execution ...
        ```

### 7.4. Mock Data Generation

*   **Convention:** `schema.py` provides `mock_truth_data`, `mock_forecast_data`, and `mock_hourly_climatology_data` functions to generate synthetic `xarray.Dataset` objects for testing. `utils.random_like` is used to populate these mocks with random data.
*   **Rationale:** Enables deterministic and reproducible tests without relying on large external data files.
*   **Examples:**
        ```python
        # weatherbench2/metrics_test.py (get_random_truth_and_forecast)
        truth = utils.random_like(schema.mock_truth_data(**data_kwargs_to_use), seed=seed)
        forecast = utils.random_like(schema.mock_forecast_data(..., **data_kwargs_to_use), seed=seed + 1)
        ```

### 7.5. Assertions in Tests

*   **Convention:** `np.testing.assert_allclose` and `xr.testing.assert_allclose` are extensively used for numerical comparisons, often with `atol` or `rtol` for floating-point precision. Custom assertion helpers (`test_utils.assert_positive`, `assert_strictly_increasing`) are also present.
*   **Rationale:** Robustly compares numerical results, accounting for floating-point inaccuracies.
*   **Examples:**
        ```python
        # weatherbench2/derived_variables_test.py
        xr.testing.assert_allclose(result, expected, atol=1e-4)

        # weatherbench2/test_utils.py
        def assert_strictly_increasing(x: Any, axis=-1, err_msg: str = '') -> None:
          assert_positive(
              np.diff(x, axis=axis),
              err_msg=f'Was not strictly increasing. {err_msg}',
          )
        ```

## 8. PERFORMANCE PATTERNS

Performance is a critical concern, especially given the large datasets involved. Optimizations are primarily achieved through efficient data handling and distributed processing.

### 8.1. Distributed Processing with Apache Beam

*   **Convention:** Apache Beam (and Google Cloud Dataflow as a runner) is the fundamental choice for distributed, scalable data processing.
*   **Rationale:** Enables out-of-core processing and parallel execution across multiple workers, essential for handling terabytes or petabytes of meteorological data.
*   **Examples:**
        ```python
        # scripts/compute_averages.py
        with beam.Pipeline(runner=RUNNER.value, argv=argv) as root:
            # ... Beam pipeline transforms ...
        ```

### 8.2. Xarray and Zarr for N-dimensional Data

*   **Convention:** Data is stored and accessed in Zarr format, which is optimized for chunked, N-dimensional arrays, allowing efficient partial reads and writes. The `xarray_beam` library is heavily utilized to seamlessly integrate Xarray datasets with Beam pipelines.
*   **Rationale:** Provides a performant and scalable storage solution for scientific datasets. Simplifies the development of distributed Xarray workflows.
*   **Examples:**
        ```python
        # scripts/compute_averages.py
        source_dataset, source_chunks = xbeam.open_zarr(INPUT_PATH.value)
        # ...
        | xbeam.ChunksToZarr(OUTPUT_PATH.value, template, target_chunks, num_threads=NUM_THREADS.value)
        ```

### 8.3. Explicit Chunking and Rechunking

*   **Convention:** Scripts explicitly define and manage data chunking at various stages of the pipeline using `input_chunks`, `working_chunks`, `output_chunks`.
*   **Rationale:** Optimal chunking is crucial for performance in distributed systems. Rechunking allows data to be reorganized for efficient processing.
*   **Examples:**
        ```python
        # scripts/compute_climatology.py
        WORKING_CHUNKS = flag_utils.DEFINE_chunks('working_chunks', ...)
        # ...
        | 'RechunkIn' >> xbeam.Rechunk(obs.sizes, input_chunks, in_working_chunks, itemsize=RECHUNK_ITEMSIZE.value)
        ```

### 8.4. Performance-related Flags

*   **Convention:** `NUM_THREADS` is a common flag to control the number of parallel I/O operations per worker. `FANOUT` is used in Beam `CombineFn`s for large datasets to improve aggregation performance. `RECHUNK_ITEMSIZE` and `MAX_MEM_GB` are used for memory management during rechunking.
*   **Rationale:** Directly addresses I/O bottlenecks and optimizes aggregation performance. Provides fine-grained control over memory allocation.
*   **Examples:**
        ```python
        # scripts/compute_averages.py
        NUM_THREADS = flags.DEFINE_integer('num_threads', None, help='Number of chunks to read/write in parallel per worker.')
        FANOUT = flags.DEFINE_integer('fanout', None, help='Beam CombineFn fanout. Might be required for large dataset.')
        # scripts/compute_climatology.py
        RECHUNK_ITEMSIZE = flags.DEFINE_integer('rechunk_itemsize', 4, help='Itemsize for rechunking.')
        # scripts/compute_derived_variables.py
        MAX_MEM_GB = flags.DEFINE_integer('max_mem_gb', 1, help='Max memory for rechunking in GB.')
        ```

### 8.5. JAX for Numerical Operations

*   **Convention:** `jax` and `jax.numpy` are used for core numerical computations, often with `jax.jit` for Just-In-Time compilation.
*   **Rationale:** Leverages JAX's capabilities for high-performance numerical computing, including automatic differentiation and GPU acceleration.
*   **Examples:**
        ```python
        # weatherbench2/regridding.py (BilinearRegridder)
        @functools.partial(jax.jit, static_argnums=0)
        def regrid_array(self, field: Array) -> jax.Array:
          batch_interp = jax.vmap(jnp.interp, in_axes=(0, None, None))
          # ...
        ```

### 8.6. Caching

*   **Convention:** `functools.lru_cache` (and a custom `dataset_safe_lru_cache` for `xarray.Dataset` objects) is used to memoize results of expensive computations.
*   **Rationale:** Avoids redundant computations, especially in functions that might be called multiple times with the same inputs.
*   **Examples:**
        ```python
        # weatherbench2/metrics.py (_pointwise_crps_spread)
        @utils.dataset_safe_lru_cache(maxsize=1)
        def _pointwise_crps_spread(
            forecast: xr.Dataset,
            ensemble_dim: str,
            skipna: bool,
        ) -> xr.Dataset:
        ```

## 9. ANTI-PATTERNS

The developer actively avoids certain practices, indicating a preference for clarity, maintainability, and robustness.

*   **Avoidance of In-Place Modification:** Operations that modify data structures in-place are generally avoided, especially for `xarray.Dataset` and `DataArray` objects. New objects are returned instead.
    *   **Rationale:** In-place modification can lead to unexpected side effects, make debugging difficult, and is problematic in distributed environments.
    *   **Anti-Example (Avoid):**
        ```python
        # Hypothetical, not found in codebase
        def bad_function(ds: xr.Dataset):
            ds['new_var'] = ds['old_var'] * 2 # Modifies ds in-place
        ```

*   **Avoidance of Unchecked Inputs:** Scripts do not proceed with execution if required flags are missing or if input values are invalid.
    *   **Rationale:** Prioritizes fail-fast behavior with clear error messages, preventing cryptic downstream failures.
    *   **Anti-Example (Avoid):**
        ```python
        # Hypothetical, not found in codebase
        input_path = flags.DEFINE_string('input_path', None)
        # ... later, without checking input_path.value is not None ...
        xbeam.open_zarr(input_path.value) # Would crash if input_path is None
        ```

*   **Avoidance of Ambiguous Time Handling:** Explicit checks and conversions are performed for time dimensions and time-related flags to ensure consistency.
    *   **Rationale:** Time data is notoriously complex in scientific computing; explicit handling prevents subtle bugs related to time alignment and interpretation.

*   **Avoidance of Unnecessary Global State (beyond flags):** While flags are global, other forms of mutable global state are not prevalent.
    *   **Rationale:** Reduces complexity and potential for side effects, making code easier to reason about and test.
    *   **Example of avoidance:** Configuration is always passed as `config.Data` or `config.Eval` objects, not accessed from a global registry.

*   **Avoidance of Excessive Commenting for Obvious Code:** Comments are used to explain *why* something is done or *what* a complex block achieves, not to re-state obvious code.
    *   **Rationale:** Keeps comments concise and valuable, preventing them from becoming outdated or redundant.
    *   **Anti-Example (Avoid):**
        ```python
        # x = x + 1 # Add 1 to x
        ```

*   **Avoidance of Magic Numbers/Strings:** Hardcoded numerical values or strings with special meaning are generally avoided. Constants are defined (e.g., `EARTH_RADIUS_M`), or values are derived from configuration.
    *   **Rationale:** Improves readability, makes code easier to modify, and reduces the chance of errors.
    *   **Example of avoidance:** Instead of `if metric == 'acc'`, `metric` is often part of a dictionary key or an enum.

*   **Avoidance of Deeply Nested Logic:** While some functions might have a few levels of nesting, excessively deep indentation or complex nested conditional/loop structures are generally avoided. Helper functions are extracted to flatten logic.
    *   **Rationale:** Deep nesting reduces readability and increases cognitive load.

*   **Avoidance of Unnecessary Abstraction:** Abstraction is used when it provides clear benefits (polymorphism, reusability, reduced complexity), but not for its own sake. Simple functions are not wrapped in classes if a class doesn't add value.
    *   **Rationale:** Over-abstraction can introduce unnecessary complexity.

## 10. DECISION TREES

These decision points highlight the developer's preferred choices when faced with common programming dilemmas.

### 10.1. When to Use a Flag vs. Hardcoding a Value

*   **Criteria:**
    *   **Configurability:** If a value might change between different runs, experiments, or deployments, it should be a flag.
    *   **User Control:** If users need to control a parameter's behavior, it should be a flag.
    *   **Domain-Specific Parameters:** Key scientific parameters (e.g., `averaging_dims`, `time_start`, `levels`) are almost always flags.
    *   **Internal Constants:** Values that are truly fixed and unlikely to change (e.g., `REALIZATION = 'realization'`) can be hardcoded as `ALL_CAPS` constants.
*   **Decision:**
    *   **Flag:** `INPUT_PATH`, `OUTPUT_PATH`, `TIME_START`, `AVERAGING_DIMS`, `NUM_THREADS`.
    *   **Hardcoded Constant:** `REALIZATION = 'realization'` (in `scripts/compute_ensemble_mean.py`), `ONE_DAY = pd.Timedelta('1d')` (in `scripts/compute_probabilistic_climatological_forecasts.py`).

### 10.2. When to Create a Private Helper Function (`_function`) vs. Inline Code

*   **Criteria:**
    *   **Reusability:** If a block of code is repeated in multiple places.
    *   **Complexity:** If a block of code is logically distinct or complex enough to warrant its own explanation.
    *   **Readability:** If extracting a block of code into a function improves the readability of the calling function (e.g., `main`).
    *   **Testability:** If a specific piece of logic needs to be unit-tested independently.
*   **Decision:**
    *   **Helper Function:** `_impose_data_selection` (reused for data filtering), `_add_derived_variables` (complex transformation logic), `_strip_offsets` (common post-processing for Beam keys).
    *   **Inline Code:** Simple assignments, single-line conditional checks, or straightforward loop iterations that don't obscure the main flow.

### 10.3. When to Use `xbeam.Rechunk`

*   **Criteria:**
    *   **Input/Output Mismatch:** When the desired output chunking differs significantly from the input chunking.
    *   **Computational Efficiency:** When a specific computation (e.g., a reduction along a dimension) would be more efficient with a different chunk layout.
    *   **Memory Constraints:** To control memory usage during processing by adjusting chunk sizes.
*   **Decision:**
    *   **Rechunk:** Frequently used before and after major processing steps in Beam pipelines (e.g., `scripts/compute_climatology.py`, `scripts/compute_derived_variables.py`, `scripts/compute_quantiles.py`).
    *   **No Rechunk:** If input chunks are already optimal for the current processing step and output.

### 10.4. When to Raise `ValueError` vs. `NotImplementedError`

*   **Criteria:**
    *   **`ValueError`:** For invalid input values or configurations that are *not* supported by the current implementation (e.g., a flag value outside an allowed range, an unexpected data shape). This indicates a problem with the user's input.
    *   **`NotImplementedError`:** For features or code paths that are conceptually valid but have not yet been implemented. This signals that the functionality is planned but not yet available.
*   **Decision:**
    *   `ValueError`: `if day_window_size <= 0 or day_window_size > 2 * 364: raise ValueError(...)` (invalid input).
    *   `NotImplementedError`: `if METHOD.value != 'explicit': raise NotImplementedError('SEEPS only tested for explicit.')` (feature not yet implemented).

### 10.5. Data Structure for Configuration

*   **Question:** How should configuration be structured?
*   **Criteria:**
    *   **Complexity:** If configuration is simple key-value pairs, a `dict` might suffice locally.
    *   **Structure & Type Safety:** If configuration has a defined schema, nested structure, and benefits from type hints, `dataclasses` are preferred.
*   **Decision:** Use `dataclasses` for structured configuration.
*   **Examples:**
        ```python
        # weatherbench2/config.py
        @dataclasses.dataclass
        class Selection:
          variables: t.Sequence[str]
          time_slice: slice
          levels: t.Optional[t.Sequence[int]] = None
          # ...
        ```

### 10.6. Handling Data Transformations

*   **Question:** Should a data transformation modify data in-place or return a new object?
*   **Criteria:**
    *   **Side Effects:** Avoid side effects on input data.
    *   **Reproducibility:** Ensure transformations are easily traceable.
    *   **Library Idiom:** Adhere to the idiom of `xarray` (which often returns new objects).
*   **Decision:** Return new `xarray.Dataset` or `DataArray` objects with transformations applied. Use `.copy()` explicitly if an internal modification is needed before returning.
*   **Examples:**
        ```python
        # weatherbench2/evaluation.py (make_latitude_increasing)
        def make_latitude_increasing(dataset: xr.Dataset) -> xr.Dataset:
          # ...
          dataset = dataset.sel(latitude=reverse_lat) # .sel returns a new object
          return dataset
        ```

### 10.7. Error Handling Strategy

*   **Question:** When to use `assert` vs. `raise ValueError` vs. `logging.warning`?
*   **Criteria:**
    *   **Programmer Error/Invariant Violation:** Use `assert` for conditions that *should never happen* if the code is logically correct.
    *   **Invalid User Input/Data:** Use `raise ValueError` for invalid inputs or data states that prevent a function from proceeding meaningfully.
    *   **Non-critical Issues/Informational:** Use `logging.warning` for situations that are unusual but don't necessarily halt execution.
*   **Decision:**
    *   `assert`: Internal logic invariants.
    *   `raise ValueError`: Invalid external inputs or data.
    *   `logging.warning`: Informational messages about non-critical deviations.
*   **Examples:**
        ```python
        # weatherbench2/derived_variables.py (PrecipitationAccumulation)
        assert np.all(timestep == timestep[0]), 'All time steps must be equal.' # Invariant

        # weatherbench2/evaluation.py (_ensure_nonempty)
        if not min(dataset.dims.values()):
          raise ValueError(f'`dataset` was empty: {dataset.dims=}.  {message}') # Invalid data

        # weatherbench2/evaluation.py (create_persistence_forecast)
        logging.warning('by-valid with evaluate_persistence is not 100% correct.') # Informational
        ```

## 11. AI AGENT INSTRUCTIONS

To seamlessly mimic the Weatherbench2 developer's coding style, an AI agent should follow this step-by-step guide.

### 11.1. Pre-Flight Checklist

Before writing any code, perform the following analysis:

1.  **Review Relevant Existing Code:**
    *   **Goal:** Understand the immediate context.
    *   **Action:** Read all `.py` files in the same directory as the target file. Pay close attention to `config.py`, `schema.py`, `metrics.py`, and `derived_variables.py` for data structures and common patterns.
    *   **Tool Use:** `read_many_files` for the current directory.
2.  **Identify Abstraction Level and Patterns:**
    *   **Goal:** Determine if the new code should be a standalone function, a method within an existing class, or a new class.
    *   **Action:** Look for existing classes that encapsulate similar logic or data. If a new meteorological concept is introduced, consider a new `dataclass` or a subclass of `DerivedVariable`, `Metric`, or `Region`.
    *   **Tool Use:** `search_file_content` for keywords related to the new functionality (e.g., "class", "def", existing variable names).
3.  **Check Naming Conventions:**
    *   **Goal:** Ensure new names align with existing patterns.
    *   **Action:** For variables, functions, and classes, refer to Section 2. Pay special attention to `xarray` dimension and variable names.
    *   **Tool Use:** `search_file_content` for similar concepts to see how they are named.
4.  **Determine Error Handling Approach:**
    *   **Goal:** Apply the appropriate error handling strategy.
    *   **Action:** Decide if an issue warrants an `assert`, `ValueError`, or `logging.warning` based on Section 4 and 10.3.
    *   **Tool Use:** Review existing error handling in related functions.
5.  **Consider Performance vs. Readability Trade-offs:**
    *   **Goal:** Balance efficiency with clarity.
    *   **Action:** For data-intensive operations, consider `xarray`'s lazy loading, Dask, or JAX. For simpler logic, prioritize straightforward Python.
    *   **Tool Use:** None directly, but be aware of the implications of chosen libraries.

### 11.2. Writing Process

When writing code, adhere to these principles:

1.  **Problem Decomposition:**
    *   **Approach:** Break down complex problems into smaller, manageable functions or methods. Favor functional transformations that return new data objects.
    *   **Example:** Instead of one large function, create `_preprocess_data`, `_compute_intermediate`, `_aggregate_results`.
2.  **Apply Naming Patterns:**
    *   **Consistency:** Use `snake_case` for functions/variables, `CamelCase` for classes. Be descriptive.
    *   **Domain Terminology:** Incorporate precise meteorological terms (e.g., `geopotential`, `specific_humidity`).
    *   **xarray Naming:** Use `latitude`, `longitude`, `level`, `time`, `lead_time`, `realization` for dimensions.
3.  **Structure Code:**
    *   **Modularity:** Place new code in the most relevant existing module. If a new, distinct concern arises, propose a new module.
    *   **Imports:** Follow the standard library, third-party, local project order. Use `import xarray as xr`, `import numpy as np`, `import typing as t`.
    *   **Dataclasses:** Use `@dataclasses.dataclass` for configuration or data-holding classes.
4.  **Implement Error Handling:**
    *   **Assertions:** Use `assert` for internal invariants.
    *   **Value Errors:** Raise `ValueError` for invalid inputs.
    *   **NaN Handling:** Explicitly manage `NaN`s, often propagating them or using `skipna`.
5.  **Add Comments/Documentation:**
    *   **Docstrings:** Provide clear docstrings for all new modules, classes, and functions, explaining their purpose, arguments, and return values.
    *   **Inline Comments:** Use sparingly, primarily to explain *why* a complex piece of logic is implemented a certain way, not *what* it does.

### 11.3. Review Criteria

Before finalizing any code, self-review against these questions:

1.  **Belonging:** Does this code feel like it was written by the original developer and belongs in this codebase? (The "sniff test").
2.  **Naming Consistency:** Are all variable, function, class, and dimension names consistent with existing patterns (Section 2)?
3.  **Abstraction Appropriateness:** Is the level of abstraction (function, method, new class) appropriate for the context? Is it too abstract or not abstract enough?
4.  **Error Handling Match:** Does the error handling strategy (assertions, `ValueError`, `NaN` management) match established patterns (Section 4 and 10.3)?
5.  **Developer's Choice:** Would the original developer likely make the same design and implementation choices? (This is the ultimate test of mimicry).
6.  **Reproducibility:** Are data transformations clear and do they avoid unexpected side effects?
7.  **Readability:** Is the code easy to read and understand for someone familiar with the domain but not necessarily the specific implementation details?