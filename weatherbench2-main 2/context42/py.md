```markdown
# Weatherbench2 Style Guide

This style guide documents the coding conventions used in the Weatherbench2 project.  It's based on the provided code examples and reflects their specific patterns rather than generic Python best practices.


## 1. Import Organization and Conventions

- Imports are grouped into categories: standard libraries, third-party libraries, and custom modules.
- Within each category, imports are alphabetically ordered.

```python
# Example from setup.py
import setuptools
import typing as t

# ... other imports (from other categories) ...
```


## 2. Class and Function Naming Patterns

- Class names use `PascalCase`.
- Function names use `snake_case`.
-  Functions related to CLI arguments often use a capitalized name with a descriptive suffix. (`INPUT_PATH`, `OUTPUT_PATH`, etc).


```python
# Example from scripts/compute_climatology.py
class ComputeClimatology(beam.PTransform):
    # ... methods ...


def compute_quantiles(input_dataset: xr.Dataset, quantiles: list) -> xr.Dataset:
    # ... function body ...
```


## 3. Type Annotation Usage

- Type annotations are used extensively, especially for function parameters and return values.
- Standard Python `typing` module is employed.


```python
# Example from scripts/compute_quantiles.py
def compute_quantiles(input_dataset: xr.Dataset, quantiles: list) -> xr.Dataset:
    # ... function body ...
```

- Type hints for flags are usually omitted, but the flag definitions include docstrings describing their type.


## 4. Docstring Style and Format

- Docstrings use Google style for both modules and functions.
-  CLI scripts include detailed example usage with comments, explaining parameters and options.


```python
# Example from scripts/compute_climatology.py
r"""CLI to compute and save climatology.

Example Usage:
# ... example usage code ...
"""
```

## 5. Code Organization and Structure

- Separate modules/scripts are often used for specific tasks (e.g., `compute_climatology`, `expand_climatology`).
- Files are organized logically in directories (e.g., `scripts`, `tests`).
- CLI arguments (`flags`) are used extensively.


## 6. Error Handling Patterns

- The `try...except SystemExit` block in `conftest.py` is a specific error handling pattern for absltest.
- Other files primarily use `try...except` blocks to catch and handle specific exceptions without broad exception handling.


## 7. Testing Conventions

- Tests use `absltest` for assertions and fixtures.
- `parameterized` is used for multiple test cases, e.g., with different values or conditions (resample_in_time_test).
- Test cases are defined using `TestCase` subclasses.
- Most tests use `flagsaver` to control flag values during test execution.
- Test methods follow the naming convention `test_<description>`.
- Use `assertCountEqual` to verify order-independent collections, when appropriate.
- Fixtures (e.g. creating temporary Zarr files) are commonly used.


## 8. Configuration and Data Class Patterns

- Configuration is primarily managed through `flags` defined with `flags.DEFINE_*` calls.
- The use of `DEFINE_string`, `DEFINE_list`, and `DEFINE_integer` are common.
- Data classes are not explicitly used. Data structures are likely assumed to be handled by libraries like `xarray`.
- Chunk sizes are defined using `DEFINE_chunks`. This includes a custom type for defining chunk specifications and handling commas and equals signs.


## 9. Unique Patterns Specific to this Codebase

- Extensive use of `xarray` and `xarray_beam` for data manipulation and parallel processing.
- `apache_beam` is used for dataflow tasks.
- Special handling for `zarr` format and data chunking.
- Using ISO 8601 timestamps for time-based selections.
-  Formatting for `gs://` paths in flags (likely for GCP usage).
- Custom functions (`SEEPSThreshold`) within scripts, tailored for the project's specific needs.
- Using `myst-nb`, `sphinx`, and `sphinx_rtd_theme` for documentation generation.
-  Use of `setuptools` for packaging (including specifying dependencies).


## 10. Style Suggestions

* **Docstrings:** Maintain consistency with the existing examples regarding the use of r" """ raw strings and use of details such as example usage.
* **Chunk Specifications:** Explicitly document the use of chunk specifications (comma-separated `key=value`).



This style guide provides a high-level overview of the conventions. More detailed information about specific aspects can be obtained by exploring the particular modules and scripts.
```